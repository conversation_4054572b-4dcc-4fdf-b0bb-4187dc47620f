import React from "react";
import type { Preview } from "@storybook/react";
import { ConfigProvider } from 'antd'

import '../src/App.css'
import '../src/tailwind.css'
import '../src/index.css'
import { theme } from '../src/configs/custom-theme';

import { ErrorBoundary } from '../src/components/common'

const App = (Story) => {
  return (
    <ErrorBoundary>
      <ConfigProvider theme={theme}>
        <Story/>
      </ConfigProvider>
    </ErrorBoundary>
  )
}


const preview: Preview = {
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
      expanded: true
    },
  },
  decorators: [App]
};

export default preview;
