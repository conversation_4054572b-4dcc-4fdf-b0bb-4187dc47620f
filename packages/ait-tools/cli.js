#!/usr/bin/env node

const fs = require('fs')
const path = require('path')
const minimist = require('minimist')
const prompts = require('prompts')
const { red, reset, green } = require('kolorist')
const { emptyDir } = require('./utils/fsExtra')

async function init() {
  const argv = minimist(process.argv.slice(2), { string: [0] })
  const cwd = process.cwd()

  let result = {}

  try {
    result = await prompts([
      {
        name: 'type',
        type: 'select',
        message: 'What do you want to create?',
        choices: [
          { title: 'Component', description: 'Generate a component', value: 'component' },
          { title: 'Page', description: 'Generate a page', value: 'page' },
        ],
        initial: 0,
      },
      {
        name: 'name',
        type: 'text',
        message: (_, { type }) => reset(`${type === 'component' ? 'Component' : 'Page'} name: `),
        validate: (name) => isValidName(name) || 'Invalid name',
      },
      {
        name: 'target',
        type: 'text',
        message: (_, { type }) => reset(`Create ${type === 'component' ? 'Component' : 'Page'} in folder: `),
        initial: (_, { name, type }) => type === 'component' ? `components/${name}` : `pages/${name}`,
      },
      {
        name: 'shouldOverwrite',
        type: (_, { target, name }) => (canSafelyOverwrite(path.join(cwd, 'src', target, name)) ? null : 'confirm'),
        message: (_, { target, name }) =>
          (`Target directory "${target}/${name}" is not empty. Remove existing files and continue?`)
      },
      {
        name: 'overwriteChecker',
        type: (_, { shouldOverwrite } = {}) => {
          if (shouldOverwrite === false) {
            throw new Error(red('✖') + ' Operation cancelled')
          }
          return null
        }
      },
      {
        name: 'createStory',
        type: 'confirm',
        message: 'Do you want to create a Storybook file?',
        initial: false,
      },
      {
        name: 'storyName',
        type: (prev) => prev ? 'text' : null,
        message: 'Enter Storybook file name:',
        validate: (name) => isValidName(name) || 'Invalid name',
      }
    ])
  } catch (cancelled) {
    console.log(cancelled.message)
    return
  }

  let { shouldOverwrite = false, type, name, createStory, storyName } = result

  const rootDir = path.join(cwd, 'src', result.target)
  const targetDir = path.join(rootDir, name)

  if (!fs.existsSync(rootDir)) {
    fs.mkdirSync(rootDir)
  }

  if (fs.existsSync(targetDir) && shouldOverwrite) {
    emptyDir(targetDir)
  } else if (!fs.existsSync(targetDir)) {
    fs.mkdirSync(targetDir)
  }

  const normalizedName = toValidName(name)
  const namespace = type === 'component' ? 'Components' : 'Pages'

  fs.writeFileSync(
    path.join(targetDir, 'types.d.ts'),
    `declare namespace App.${namespace} {
  export type ${normalizedName}Props = {}
  export type ${normalizedName}Ref = {}
}
`
  )

  fs.writeFileSync(
    path.join(targetDir, 'index.tsx'),
    `import React from 'react'

const ${normalizedName} = React.forwardRef<
  App.${namespace}.${normalizedName}Ref,
  App.${namespace}.${normalizedName}Props
>((_props, ref) => {

  React.useImperativeHandle(ref, () => ({}), [])

  return (
    <div>${normalizedName} ${type === 'component' ? 'Component' : 'Page'}</div>
  )  
})

${normalizedName}.displayName = '${normalizedName}'

export default React.memo(${normalizedName})
`
  )

  if (createStory && storyName) {
    const storiesDir = path.join(cwd, 'src', 'Stories', 'components', storyName)
    if (!fs.existsSync(storiesDir)) {
      fs.mkdirSync(storiesDir)
    }
    fs.writeFileSync(
      path.join(storiesDir, `${capitalizeFirstLetter(storyName)}.stories.tsx`),
      `
import type { Meta, StoryObj } from "@storybook/react";
import ${normalizedName} from '@/${result.target}/${name}';

const meta = {
  title: "Components/${normalizedName}",
  component: ${normalizedName},
} satisfies Meta<typeof ${normalizedName}>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
`
    )
  }

  console.log(green('✔') + ' Done')
}

function canSafelyOverwrite(dir) {
  return !fs.existsSync(dir) || fs.readdirSync(dir).length === 0
}

function isValidName(name) {
  return /^(?:@[a-z0-9-*~][a-z0-9-*._~]*\/)?[a-z0-9-~][a-z0-9-._~]*$/.test(name)
}

Object.defineProperty(String.prototype, 'capitalize', {
  value: function() {
    return this.charAt(0).toUpperCase() + this.slice(1);
  },
  enumerable: false
});

function capitalizeFirstLetter(str) {
  if (!str) return "";
  return str.charAt(0).toUpperCase() + str.slice(1);
}

function toValidName(name) {
  return name
    .trim()
    .toLowerCase()
    .replace(/\s+/g, '-')
    .split('-')
    .filter(Boolean)
    .map(chunk => chunk.capitalize())
    .join('')
}

init().catch((e) => {
  console.error(e)
})
