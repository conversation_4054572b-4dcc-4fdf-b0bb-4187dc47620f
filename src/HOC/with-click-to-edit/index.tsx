import React, { useCallback, useState, CSSProperties } from 'react'
import { Icon } from '@iconify/react'

import { isFunction } from 'lodash'
import { cn } from '@/utils'
import { theme } from 'antd'
import { Typography, Button } from '@/atoms'

export type WrappedBaseProps<TValue extends unknown> = {
  value?: TValue
  render?(value?: TValue): React.ReactNode
  wrappedClassName?: string,
  editable?: boolean
  hasInitialView?: boolean
  hasPopupContainer?: boolean
  showEditIcon?: boolean
  placeholder?: React.ReactNode
}
export type WrappedProps<TValue = unknown, TProps = {}> = WrappedBaseProps<TValue> & TProps

// export type BaseComponentProps = {
//   onBlur?: React.HTMLProps<HTMLElement>['onBlur']
// }

/**
 * The HOC allow to show " YourEditor" component when click to value.
 * @example
 *        import withClickToEditHOC from '@/HOC';
 *        import Input, { type InputProps } from 'antd';
 *        ...
 *        const InputWithEditTrigger = withClickToEditHOC<InputProps['value'], InputProps>(Input)
 *        ....
 *        const YourComponent = (props) => {
 *          
 *           return <InputWithEditTrigger />
 *        }
 * @see {@link src/stories/HOC/with-click-to-edit.stories.tsx}
 * @param {React.Component} Component The component that you want to use.
 */
export const withClickToEditHOC = <TValue extends unknown, TProps extends object = {}>(
  Component: React.ComponentType<TProps>
) => {
  const ClickToEditHandler = (props: WrappedProps<TValue, TProps>) => {
    const { value, render, wrappedClassName, editable = true, hasInitialView = true, hasPopupContainer, placeholder, ...restProps } = props
    const [editing, setEditing] = useState(false)
    const containerRef = React.useRef<HTMLDivElement>(null)
    const { token } = theme.useToken()

    const showComponent = useCallback(() => {
      editable && setEditing(true)
    }, [editable])

    const showLabel = useCallback(() => setEditing(false), [])

    const onBlurHandler = useCallback<React.FocusEventHandler<HTMLDivElement>>((e) => {
      if (!containerRef.current?.contains(e.relatedTarget)) showLabel()
    }, [showLabel])

    const overlayStyle = React.useMemo<CSSProperties>(() => ({
      background: token.colorFillQuaternary
    }), [token])

    const placeholderStyle = React.useMemo<CSSProperties>(() => ({
      color: !value ? token.colorTextQuaternary : ''
    }), [token, value])

    return (
      <div
        ref={containerRef}
        className={cn('w-full group flex items-center relative min-h-[30px] cursor-pointer', wrappedClassName)}
        onClick={showComponent}
        onBlur={onBlurHandler}
        tabIndex={-1}
      >
        <div 
          className="absolute w-full h-full top-0 pointer-events-none rounded-md opacity-0 group-hover:opacity-100 ease-in-out duration-300"
          style={overlayStyle}
        ></div>
        {editing || !hasInitialView ? (
           <Component
            value={value}
            {...(!!hasPopupContainer && {getPopupContainer: () => containerRef.current as HTMLDivElement})}
            {...(restProps as TProps)} 
          />
        ) : isFunction(render) ? (
          render(value)
        ) : (
          <div className='flex gap-2 items-center w-full h-full'>
            <Typography.Text 
              className='flex-auto ml-3 text-sm truncate'
              style={placeholderStyle}
            >
              {value 
                ? value as React.ReactNode
                : placeholder ?? ''
              }
            </Typography.Text>
          </div>
        )}
        {editable && hasInitialView && (
          <Button
            type={'text'}
            icon={<Icon icon="fluent:edit-12-regular" />}
            className={cn(
              'group-hover:opacity-100 opacity-0 absolute right-0 top-0 pointer-events-none',
              editing && 'hidden'
            )}
          />
        )}
      </div>
    )
  }

  return ClickToEditHandler
}
