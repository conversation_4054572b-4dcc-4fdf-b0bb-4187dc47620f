/** ------------------- WARNING ------------------- 
 * PLEASE NOTE: DO NOT USE IN ATOMS AND COMPONENTS FOLDER
 * ------------------- -------------------  -------
*/
import { atom } from 'recoil'

export const detailStaffInfo = atom<App.DataTypes.Staff | undefined>({
  key: 'staffDetailState',
  default: undefined,
})

export const detailCustomerInfo = atom<App.DataTypes.Customer | undefined>({
  key: 'customerDetailState',
  default: undefined,
})

export const detailPartnerInfo = atom<App.DataTypes.Partner | undefined>({
  key: 'partnerDetailState',
  default: undefined,
})

export const detailWorkerInfo = atom<App.DataTypes.Worker | undefined>({
  key: 'workerDetailState',
  default: undefined,
})

export const detailVendorInfo = atom<App.DataTypes.Vendor | undefined>({
  key: 'vendorDetailState',
  default: undefined,
})

export const detailCollaboratorInfo = atom<App.DataTypes.Collaborator | undefined>({
  key: 'collaboratorDetailState',
  default: undefined,
})

export const detailOrganizationInfo = atom<App.DataTypes.Organization | undefined>({
  key: 'organizationDetailState',
  default: undefined,
})