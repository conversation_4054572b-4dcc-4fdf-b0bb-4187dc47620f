import React from 'react';
import { useNavigate, useLocation } from 'react-router';

import { StorageService } from '@/services';
import AuthService from '@/services/auth';
import { appConfigs } from '@/configs';

type AuthContext = App.Authentication.AuthContext;

async function asyncDoNothing() { return undefined; }

const authContext = React.createContext<AuthContext>({
  user: undefined,
  info: undefined,
  login: asyncDoNothing,
  logout: asyncDoNothing,
  refreshToken: asyncDoNothing,
  redirectToLogin: function() {},
  updatePassword: async function() { return {} },
  isAuthenticated: function() { return false; },
  performAuthentication: asyncDoNothing,
});

const storage = new StorageService();
const authService = new AuthService();

const clearAuthInfoCache = () => {
  storage.local.delete('access-token');
  storage.local.delete('refresh-token');
  storage.local.delete('user');
};

const cacheAuthInfo = (user: AuthContext['user'], info: AuthContext['info']) => {
  info?.accessToken && storage.local.save('access-token', info?.accessToken);
  info?.refreshToken && storage.local.save('refresh-token', info?.refreshToken);
  user && storage.local.save('user', user);
};

export const useAuthProvide = (): AuthContext => {
  const [user, setCurrentUser] = React.useState<App.DataTypes.User | undefined>();
  const [authInfo, setAuthInfo] = React.useState<App.Authentication.AuthInfo>();
  const [checking, setCheckingStatus] = React.useState<boolean>(true);
  const navigate = useNavigate();
  const location = useLocation();

  const redirectToUpdatePassword = React.useCallback((callbackUrl?: string) => {
    navigate(callbackUrl || '/');
  }, [navigate]);

  const redirectToOriginPage = React.useCallback((callbackUrl?: string) => {
    window.location.href = callbackUrl ? decodeURIComponent(callbackUrl) : appConfigs.BASE_URL;
  }, [navigate]);

  const redirectToLogin = React.useCallback<AuthContext['redirectToLogin']>(() => {
    const currentUrl = window.location.href;
    const url = new URL(currentUrl);
    navigate({ pathname: 'login', search: url.search || 'callbackUrl=' + encodeURIComponent(currentUrl) });
  }, [navigate, location]);

  // Hàm login được gọi trong trang login.
  const login = React.useCallback<AuthContext['login']>(async (username, password, callbackUrl) => {
    const result = await authService.login(username, password);

    if (result?.data?.accessToken) {
      setCurrentUser(result.data.user);
      setAuthInfo(result.data);
      cacheAuthInfo(result.data.user, result.data);

      if (callbackUrl) {
        if (result.data.user.mustUpdatePassword) {
          redirectToUpdatePassword(callbackUrl);
        } else {
          redirectToOriginPage(callbackUrl);
        }
      } else {
        return {
          accessToken: result.data.accessToken,
          refreshToken: result.data.refreshToken,
          updatePwdToken: result.data.updatePwdToken,
          user: result.data.user,
        }
      }

      return undefined;
    }
  }, [cacheAuthInfo, redirectToUpdatePassword, redirectToOriginPage]);

  // Logout tài khoản hiện tại và xóa tất cả các dữ liệu liên quan đến phiên đăng nhập.
  const logout = React.useCallback<AuthContext['logout']>(async (options = { redirect: true }) => {
    setCurrentUser(undefined);
    setAuthInfo(undefined);
    clearAuthInfoCache();
    options?.redirect && redirectToLogin();
  }, [redirectToLogin]);

  // Update mật khẩu mới trong trường hợp tài khoản lần đầu tiên đăng nhập thành công.
  const updatePassword = React.useCallback<AuthContext['updatePassword']>(async (token, newPassword) => {
    const result = await authService.updatePassword(newPassword, token);

    if (result.data?.updated) {
      if (user) {
        setCurrentUser(old => ({ ...old, mustUpdatePassword: false } as AuthContext['user']));
        storage.local.save('user', {...user, mustUpdatePassowrd: false });
      }
    }

    return result;
  }, [user]);

  const isAuthenticated = React.useCallback<AuthContext['isAuthenticated']>(() => {
    return !!authInfo?.accessToken && !!user;
  }, [authInfo, user]);

  // Lấy dữ liệu phiên đăng nhập từ local storage.
  const loadUserAndAuthInfoFromStorage = React.useCallback<
    () => Promise<{ user: AuthContext['user'], info: AuthContext['info'] } | undefined>
  >(async () => {
    const accessToken = storage.local.load('access-token');
    const refreshToken = storage.local.load('refresh-token');
    const user = storage.local.load<AuthContext['user'] | undefined>('user');

    if (!accessToken || !refreshToken || !user) return undefined;

    setAuthInfo({ accessToken, refreshToken, user });
    setCurrentUser(user);

    return { user, info: { accessToken, refreshToken, user } };
  }, []);

  const refreshToken = React.useCallback(async (info?: AuthContext['info']) => {
    if (!authInfo?.refreshToken && !info) return undefined;

    const result = await authService.refreshToken((info?.refreshToken || authInfo?.refreshToken) as string);

    if (result.data?.accessToken) {
      setAuthInfo(old => ({...old, accessToken: result.data?.accessToken }));
      storage.local.save('access-token', result.data.accessToken);
      return { ...authInfo, accessToken: result.data.accessToken };
    }

    return undefined;
  }, [authInfo]);

  // Kiểm tra và xác thực.
  const performAuthentication = React.useCallback<
    AuthContext['performAuthentication']
  >(async (options = { redirectToLogin: true }) => {
    setCheckingStatus(true);

    if (isAuthenticated()) {
      setCheckingStatus(false);
      if (options.redirectToLogin && authInfo?.user?.mustUpdatePassword) {
        redirectToLogin();
        return;
      }
      return authInfo;
    }

    const result = await loadUserAndAuthInfoFromStorage();

    if (!result || !result.info?.accessToken) {
      setCheckingStatus(false);
      if (options.redirectToLogin) {
        redirectToLogin();
      }
      return undefined;
    }

    // verify access token hoặc lấy thử lấy token mới với refresh token
    const verifyResult = await authService.verify(result.info.accessToken);

    if (verifyResult?.data?.verified) {
      return authInfo;
    }

    // thử lấy access token mới với refresh token
    const refreshResult = await refreshToken(result.info);

    if (!refreshResult?.accessToken) {
      if (options.redirectToLogin || (options.redirectToLogin && result.user?.mustUpdatePassword)) {
        logout({ redirect: true });
        return;
      }

      return undefined;
    }

    setCheckingStatus(false);

    // nếu không được xác thực thì chuyển đến trang login
    if (options.redirectToLogin) {
      redirectToLogin();
    }

    return undefined;
  }, [authInfo, isAuthenticated, loadUserAndAuthInfoFromStorage, redirectToLogin, refreshToken]);

  return {
    user,
    info: authInfo,
    checking,
    login,
    logout,
    refreshToken,
    redirectToLogin,
    isAuthenticated,
    updatePassword,
    performAuthentication
  };
};

const useAuth = () => {
  const auth = React.useContext(authContext);
  return auth;
};

export const AuthProvider = authContext.Provider;

export const withAuth = <TProps extends object = {}>(Component: React.ComponentType<TProps>) => {
  const WrappedComponent = (props: TProps) => {
    const auth = useAuthProvide();
    return (
      <AuthProvider value={auth} >
        <Component {...props} />
      </AuthProvider>
    )
  };

  return WrappedComponent;
};

export default useAuth;
