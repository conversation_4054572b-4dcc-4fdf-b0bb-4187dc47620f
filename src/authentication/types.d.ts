declare namespace App.Authentication {
  type AuthInfo = {
    accessToken?: string,
    refreshToken?: string,
    updatePwdToken?: string,
    user?: App.DataTypes.User,
  }

  type AuthContext = {
    /** Tài khoản hiện tại đã đăng nhập */
    user?: App.DataTypes.User,

    /** Thông tin đăng nhập */
    info?: AuthInfo,

    /** Check authenticated user state */
    checking?: boolean;

    /**
     * Đăng nhập vào hệ thống.
     * @param {string} username Tài khoản đăng nhập
     * @param {string} password Mật khẩu
     * @param {string} callbackUrl Url sẽ được gọi lại nếu đăng nhập thành công. Nếu không truyền tham số này, hàm login sẽ trả về kết quả đăng nhập để logic bên ngoài tự xử lý.
     * @returns {Promise<void>}
     */
    login: (username: string, password: string, callbackUrl?: string) => Promise<AuthInfo | undefined>,

    /**
     * Đăng xuất khỏi thiết bị hiện tại.
     * @returns {Promise<void>}
     */
    logout: (options?: { redirect: boolean }) => Promise<void>,

    refreshToken: () => Promise<AuthInfo | undefined>,

    /**
     * Chuyển hướng người dùng sang trang đăng nhập.
     * @returns {void}
     */
    redirectToLogin: () => void,

    /**
     * Kiểm tra user đã đăng nhập hay chưa.
     * @returns {boolean}
     */
    isAuthenticated: () => boolean,

    /**
     * Được gọi khi phải cập nhật mật khẩu mới khi user đăng nhập thành công lần đầu tiên.
     * @param {string} updatePwdToken Token dùng đề thay đổi mật khẩu - được lấy trong kết quả login thành công.
     * @param {string} newPassword Mật khẩu người dùng mới.
     * @returns {Promise<void>}
     */
    updatePassword: (updatePwdToken: string, newPassword: string) => ReturnType<App.Services.Auth.updatePassword>,

    /**
     * Kiểm tra và xác thực dữ liệu của phiên đăng nhập hiện tại.
     * @returns {Promise<boolean | void>}
     */
    performAuthentication: (options?: { redirectToLogin?: boolean }) => Promise<AuthInfo | undefined>
  }
}
