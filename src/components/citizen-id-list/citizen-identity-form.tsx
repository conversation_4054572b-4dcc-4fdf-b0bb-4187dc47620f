import React from "react";
import { Icon } from "@iconify/react";
import { Form, type FormItemProps, InputRef } from "antd";
import dayjs from "dayjs";
import { Gender } from "@/constants";
import { Button, Modal, Input, DatePicker, Select, message } from "@/atoms";
import { cn, getDiff } from "@/utils";

import FormItems from "@/components/form-items";
import { CitizenIdentityService } from "@/services";

const service = new CitizenIdentityService();

const requiredRule: FormItemProps["rules"] = [
  { required: true, message: "Không được để trống" }
];

const InternalCitizenIdentityForm = React.forwardRef(
  (
    props: App.Components.CitizenIdentity.FormProps,
    ref: React.Ref<App.Components.CitizenIdentity.FormRef>
  ) => {
    const {
      onFinish,
      form = Form!.useForm<App.Components.CitizenIdentity.ValueType>()[0],
      ...restProps
    } = props;
    const numberRef = React.useRef<InputRef>(null);

    //Ref
    const focusForm = React.useCallback(() => {
      numberRef.current?.focus();
    }, []);

    //Ref
    const submitForm = React.useCallback(async () => {
      try {
        const values = await form.validateFields();
        const newValues = { ...values };

        if (onFinish) {
          onFinish(newValues);
        }
        form.resetFields();
      } catch (error) {
        console.log("error", error);
      }
    }, [form, onFinish]);

    const setValue = React.useCallback<
      NonNullable<App.Components.CitizenIdentity.FormRef["setValue"]>
    >(
      (data) => {
        form.setFieldsValue(data);
      },
      [form]
    );

    React.useImperativeHandle(
      ref,
      () => ({
        focus: focusForm,
        submit: submitForm,
        setValue,
        reset: () => form.resetFields()
      }),
      [focusForm, submitForm, form, setValue]
    );

    React.useEffect(() => {
      focusForm();
    }, [focusForm]);

    return (
      <div className="overflow-y-auto px-4">
        <Form
          form={form}
          layout="vertical"
          className="grid grid-cols-12 gap-4"
          name="citizen-identity-form"
          {...restProps}
        >
          <Form.Item
            name="number"
            label="Số Căn cước công dân"
            rules={requiredRule}
            className="col-span-6"
          >
            <Input ref={numberRef} />
          </Form.Item>
          <Form.Item
            name="fullname"
            label="Họ và tên"
            rules={requiredRule}
            className="col-span-6"
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="gender"
            label="Giới tính"
            className="col-span-4"
            rules={requiredRule}
          >
            <Select className="w-full">
              <Select.Option key={Gender.MALE}>Nam</Select.Option>
              <Select.Option key={Gender.MALE}>Nữ</Select.Option>
              <Select.Option key={Gender.OTHER}>Khác</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="dob"
            label="Ngày sinh"
            className="col-span-4"
            rules={requiredRule}
          >
            <DatePicker className="w-full" />
          </Form.Item>
          <Form.Item
            name="nationality"
            label="Quốc tịch"
            rules={requiredRule}
            className="col-span-4"
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="placeOfOrigin"
            className="col-span-12"
            label="Quê quán"
            rules={requiredRule}
          >
            <FormItems.Address />
          </Form.Item>

          <Form.Item
            name="placeOfResidence"
            label="Địa chỉ thường trú"
            rules={requiredRule}
            className="col-span-12"
          >
            <FormItems.Address />
          </Form.Item>

          <Form.Item
            name="dateOfIssue"
            label="Ngày cấp"
            rules={requiredRule}
            className="col-span-3"
          >
            <DatePicker className="w-full" />
          </Form.Item>

          <Form.Item
            name="placeOfIssue"
            label="Nơi cấp"
            rules={requiredRule}
            className="col-span-3"
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="dateOfExpiry"
            label="Có giá trị tới"
            rules={requiredRule}
            className="col-span-3"
          >
            <DatePicker className="w-full" />
          </Form.Item>

          <Form.Item
            name="issuedBy"
            label="Cơ quan cấp"
            rules={requiredRule}
            className="col-span-3"
          >
            <Input />
          </Form.Item>
        </Form>
      </div>
    );
  }
);

const WithModel = React.forwardRef(
  (
    props: App.Components.CitizenIdentity.ButtonTriggerProps,
    ref: React.Ref<App.Components.CitizenIdentity.ButtonTriggerRef>
  ) => {
    const {
      owner,
      value,
      children,
      modelProps: { className: modalClassName, ...restModelProps } = {},
      buttonProps,
      onSuccess
    } = props;
    const [open, setOpen] = React.useState(false);
    const [messageApi, messageContextHolder] = message.useMessage();

    const formRef = React.useRef<App.Components.CitizenIdentity.FormRef>(null);

    const [data, setData] = React.useState<
      App.Components.CitizenIdentity.ValueType | undefined
    >(undefined);
    const showModel = React.useCallback<
      NonNullable<App.Components.CitizenIdentity.ButtonTriggerRef["show"]>
    >((data) => {
      formRef.current?.reset();
      setOpen(true);
      setTimeout(() => {
        if (data && formRef.current) {
          const { dob, dateOfIssue, dateOfExpiry, ...restData } = data;

          const newData = {
            dob: dayjs(dob),
            dateOfIssue: dayjs(dateOfIssue),
            dateOfExpiry: dayjs(dateOfExpiry),
            ...restData
          };

          setData(newData);

          formRef.current.setValue(newData);
        }
      }, 100);
    }, []);

    const hideModel = React.useCallback(() => {
      setOpen(false);
      setData(undefined);
    }, []);

    const handleSubmitForm = React.useCallback(() => {
      formRef.current?.submit();
    }, []);

    const handleFormFinish = React.useCallback(
      (values: App.Components.CitizenIdentity.ValueType) => {
        const payload = {
          ...values,
          owner
        };
        payload.dob = dayjs(payload.dob).format("MM/DD/YYYY");
        payload.dateOfIssue = dayjs(payload.dateOfIssue).format("MM/DD/YYYY");
        payload.dateOfExpiry = dayjs(payload.dateOfExpiry).format("MM/DD/YYYY");
        try {
          service.createCitizenIdentity(payload).then((result) => {
            const { code } = result;
            if (code === "200") {
              messageApi.open({
                type: "success",
                content: "Tạo CCCD thành công"
              });
              onSuccess?.();
            } else {
              messageApi.open({
                type: "error",
                content: "Tạo CCCD không thành công"
              });
            }

            formRef.current?.reset();
            hideModel();
          });
        } catch (error) {
          console.log(error);
        }
      },
      []
    );

    const handleFormUpdate = React.useCallback(
      (values: App.Components.CitizenIdentity.ValueType) => {
        if (!data) return;
        const { _id, attachments, createdAt, updatedAt, owner, ...restData } =
          data as unknown as App.DataTypes.CitizenIdentityCard;
        const diff = getDiff<App.Components.CitizenIdentity.ValueType>(
          restData as unknown as App.Components.CitizenIdentity.ValueType,
          values
        );

        const payload = { ...diff };

        if (payload.dob) {
          payload.dob = dayjs(payload.dob).format("MM/DD/YYYY");
        }

        if (payload.dateOfIssue) {
          payload.dateOfIssue = dayjs(payload.dateOfIssue).format("MM/DD/YYYY");
        }

        if (payload.dateOfExpiry) {
          payload.dateOfExpiry = dayjs(payload.dateOfExpiry).format(
            "MM/DD/YYYY"
          );
        }

        try {
          service
            .updateCitizenIdentity(_id as string, payload)
            .then((result) => {
              const { code } = result;
              if (code === "200") {
                messageApi.open({
                  type: "success",
                  content: "Tạo CCCD thành công"
                });
                onSuccess?.();
              } else {
                messageApi.open({
                  type: "error",
                  content: "Tạo CCCD không thành công"
                });
              }

              formRef.current?.reset();
              hideModel();
            });
        } catch (error) {
          console.log(error);
        }
      },
      [data]
    );

    React.useImperativeHandle(
      ref,
      () => ({
        show: showModel,
        hide: hideModel,
        citizenIdentityForm: formRef
      }),
      []
    );

    return (
      <>
        {messageContextHolder}
        <div
          onClick={() => {
            showModel();
          }}
        >
          {children || (
            <Button {...buttonProps}>
              <div className="center gap-1">
                <Icon icon="icon-park-outline:plus" />
                <span>Tạo CCCD mới</span>
              </div>
            </Button>
          )}
        </div>

        <Modal
          {...restModelProps}
          title="Tạo thông tin Căn cước công dân mới"
          open={open}
          onCancel={hideModel}
          onOk={handleSubmitForm}
          className={cn("min-w-[800px]", modalClassName)}
          centered
          okText={data ? "Cập nhật" : "Tạo mới"}
          footer={(originNode) => {
            return (
              <div className="center gap-2 *:min-w-[150px]">{originNode}</div>
            );
          }}
        >
          <InternalCitizenIdentityForm
            ref={formRef}
            value={value}
            onFinish={data ? handleFormUpdate : handleFormFinish}
          />
        </Modal>
      </>
    );
  }
);

type CitizenIdentityFormType = typeof InternalCitizenIdentityForm & {
  Button: typeof WithModel;
};

const CitizenIdentityForm =
  InternalCitizenIdentityForm as CitizenIdentityFormType;
CitizenIdentityForm.Button = WithModel;

export default CitizenIdentityForm;
