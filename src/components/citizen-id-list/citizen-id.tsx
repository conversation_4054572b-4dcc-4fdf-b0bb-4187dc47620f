import React from "react";
import { Typography } from "@/atoms";
import { Image } from "antd";
import dayjs from "dayjs";
import { DATE_FORMAT } from "@/contants";

const CitizenId = (props: App.Components.CitizenIdList.CitizenIdProps) => {
  const { data } = props;

  return (
    <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <div className="flex flex-col gap-2">
        <Typography.Text>Số</Typography.Text>
        <Typography.Text className="font-semibold text-sm">
          {data.no}
        </Typography.Text>
      </div>
      <div className="flex flex-col gap-2">
        <Typography.Text>Họ và tên</Typography.Text>
        <Typography.Text className="font-semibold text-sm">
          {data.name}
        </Typography.Text>
      </div>
      <div className="flex flex-col gap-2">
        <Typography.Text>Ng<PERSON>y sinh</Typography.Text>
        <Typography.Text className="font-semibold text-sm">
          {dayjs(data.dateOfBirth).format(DATE_FORMAT)}
        </Typography.Text>
      </div>
      <div className="flex flex-col gap-2">
        <Typography.Text>Giới tính</Typography.Text>
        <Typography.Text className="font-semibold text-sm">
          {data.sex}
        </Typography.Text>
      </div>
      <div className="flex flex-col gap-2">
        <Typography.Text>Quốc tịch</Typography.Text>
        <Typography.Text className="font-semibold text-sm">
          {data.nationality}
        </Typography.Text>
      </div>
      <div className="flex flex-col gap-2">
        <Typography.Text>Quê quán</Typography.Text>
        <Typography.Text className="font-semibold text-sm">
          {data.placeOfOrigin}
        </Typography.Text>
      </div>
      <div className="flex flex-col gap-2">
        <Typography.Text>Nơi thường trú</Typography.Text>
        <Typography.Text className="font-semibold text-sm">
          {data.placeOfResidence}
        </Typography.Text>
      </div>
      <div className="flex flex-col gap-2">
        <Typography.Text>Ngày cấp</Typography.Text>
        <Typography.Text className="font-semibold text-sm">
          {dayjs(data.dateOfIssue).format(DATE_FORMAT)}
        </Typography.Text>
      </div>
      <div className="flex flex-col gap-2">
        <Typography.Text>Có giá trị đến</Typography.Text>
        <Typography.Text className="font-semibold text-sm">
          {dayjs(data.dateOfExpiry).format(DATE_FORMAT)}
        </Typography.Text>
      </div>
      <div className="flex flex-col gap-2">
        <Typography.Text>Nơi cấp</Typography.Text>
        <Typography.Text className="font-semibold text-sm">
          {data.placeOfIssue}
        </Typography.Text>
      </div>
      <div className="flex flex-col gap-2">
        <Typography.Text>Cơ quan cấp</Typography.Text>
        <Typography.Text className="font-semibold text-sm">
          {data.placeOfIssue}
        </Typography.Text>
      </div>
      <div className="flex col-start-1 col-span-2 md:col-span-4 row-span-1 flex-col gap-2">
        <Typography.Text>Ảnh</Typography.Text>
        <div className="md:flex gap-2">
          {data.attachments.map((attachment) => (
            <Image width={"full"} src={attachment} />
          ))}
        </div>
      </div>
    </div>
  );
};

export default React.memo(CitizenId);
