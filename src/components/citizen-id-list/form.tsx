import React from "react";
import { DatePicker, Form, Input, Select, Upload } from "antd";
import { Button } from "@/atoms";
import { GENDER_OPTIONS } from "@/contants/commons";

const CitizenIdForm = (
  props: App.Components.CitizenIdList.CitizenIdFormProps
) => {
  const {
    form = Form.useForm<App.Components.CitizenIdList.CitizenIdDataType>()[0]
  } = props;

  return (
    <Form<App.Components.CitizenIdList.CitizenIdDataType>
      layout="vertical"
      form={form}
    >
      <Form.Item<App.Components.CitizenIdList.CitizenIdDataType>
        label="Số"
        name="no"
      >
        <Input />
      </Form.Item>
      <Form.Item<App.Components.CitizenIdList.CitizenIdDataType>
        label="Họ và tên"
        name="name"
      >
        <Input />
      </Form.Item>
      <Form.Item<App.Components.CitizenIdList.CitizenIdDataType>
        label="<PERSON><PERSON><PERSON> sinh"
        name="dateOfBirth"
      >
        <DatePicker />
      </Form.Item>
      <Form.Item<App.Components.CitizenIdList.CitizenIdDataType>
        label="Giới tính"
        name="sex"
      >
        <Select defaultValue="male" options={GENDER_OPTIONS} />
      </Form.Item>
      <Form.Item<App.Components.CitizenIdList.CitizenIdDataType>
        label="Quốc tịch"
        name="nationality"
        initialValue="Việt Nam"
      >
        <Input />
      </Form.Item>
      <Form.Item<App.Components.CitizenIdList.CitizenIdDataType>
        label="Quê quán"
        name="placeOfOrigin"
      >
        <Input />
      </Form.Item>
      <Form.Item<App.Components.CitizenIdList.CitizenIdDataType>
        label="Nơi thường trú"
        name="placeOfResidence"
      >
        <Input />
      </Form.Item>
      <Form.Item<App.Components.CitizenIdList.CitizenIdDataType>
        label="Ngày cấp"
        name="dateOfIssue"
      >
        <DatePicker />
      </Form.Item>
      <Form.Item<App.Components.CitizenIdList.CitizenIdDataType>
        label="Ngày hết hạn"
        name="dateOfExpiry"
      >
        <DatePicker />
      </Form.Item>
      <Form.Item<App.Components.CitizenIdList.CitizenIdDataType>
        label="Nơi cấp"
        name="placeOfIssue"
      >
        <Input />
      </Form.Item>
      <Form.Item<App.Components.CitizenIdList.CitizenIdDataType>
        label="Cơ quan cấp"
        name="placeOfIssue"
      >
        <Input />
      </Form.Item>
      <Form.Item<App.Components.CitizenIdList.CitizenIdDataType>
        label="Ảnh CMND"
        name="attachments"
      >
        <Upload>
          <Button>Upload</Button>
        </Upload>
      </Form.Item>
    </Form>
  );
};

export default CitizenIdForm;
