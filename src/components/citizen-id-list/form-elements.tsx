import { GenderMap } from "@/constants";
import { convertMapToArray } from "@/constants/utils";
import AddressElement from "../new-entity-form/address-element";
import { Uploader } from "@/components";

const genderOptions = convertMapToArray(GenderMap);

export const citizenIdentityFormElementTypes = {
  attachment: Uploader.AttachmentFormItem,
  address: AddressElement
} 

export const citizenIdentityFormElements =
  (ownerId: string, type: App.Components.CitizenIdList.CitizenIdListProps["type"]) => {
    return [
      {
        name: "_id",
        hidden: true
      },
      {
        name: "owner",
        hidden: true
      },
      {
        name: "number",
        label: "Số căn cước công dân",
        type: "input",
        rules: [
          { required: true, message: "Số căn cước công dân là bắt buộc" }
        ],
        inputProps: {
          className: "w-full",
          placeholder: "<PERSON>hập số căn cước công dân"
        }
      },
      {
        name: "fullname",
        label: "Họ và tên",
        type: "input",
        rules: [{ required: true, message: "Họ và tên là bắt buộc" }],
        inputProps: {
          placeholder: "Nhập họ và tên"
        }
      },
      {
        name: "gender",
        label: "Giới tính",
        type: "select",
        rules: [{ required: true, message: "Giới tính là bắt buộc" }],
        inputProps: {
          className: "w-full",
          options: genderOptions
        }
      },
      {
        name: "dob",
        label: "Ngày sinh",
        type: "datePicker",
        rules: [{ required: true, message: "Ngày sinh là bắt buộc" }],
        inputProps: {
          className: "w-full",
          placeholder: "Chọn ngày sinh"
        }
      },
      {
        name: "nationality",
        label: "Quốc tịch",
        rules: [{ required: true, message: "Quốc tịch là bắt buộc" }],
        type: "input",
        inputProps: {
          className: "w-full",
          placeholder: "Nhập quốc tịch"
        }
      },
      {
        // name: "placeOfOrigin",
        label: "Quê quán",
        type: "address",
        cols: 2,
        rules: [{ required: true, message: "Quê quán là bắt buộc" }],
        inputProps: {
          name: ["placeOfOrigin"],
          className: "w-full",
          isRequired: true,
          requires: ["province", "district", "ward"]
        }
      },
      {
        // name: "placeOfResidence",
        label: "Địa chỉ thường trú",
        type: "address",
        cols: 2,
        rules: [{ required: true, message: "Địa chỉ thường trú là bắt buộc" }],
        inputProps: { 
          name: ["placeOfResidence"],
          className: "w-full",
          isRequired: true,
          requires: ["province", "district", "ward"]
        }
      },
      {
        name: "dateOfIssue",
        label: "Ngày cấp",
        type: "datePicker",
        rules: [{ required: true, message: "Ngày cấp là bắt buộc" }],
        inputProps: {
          className: "w-full",
          placeholder: "Chọn ngày cấp"
        }
      },
      {
        name: "dateOfExpiry",
        label: "Ngày hết hạn",
        type: "datePicker",
        rules: [{ required: true, message: "Ngày hết hạn là bắt buộc" }],
        inputProps: {
          className: "w-full",
          placeholder: "Chọn ngày hết hạn"
        }
      },
      {
        name: "placeOfIssue",
        label: "Nơi cấp",
        type: "input",
        inputProps: {
          className: "w-full",
          placeholder: "Nhập nơi cấp"
        }
      },
      {
        name: "issuedBy",
        label: "Cơ quan cấp",
        type: "input",
        inputProps: {
          className: "w-full",
          placeholder: "Nhập cơ quan cấp"
        }
      },
      // {
      //   name: "attachments",
      //   label: "Tải lên",
      //   type: "attachment",
      //   inputProps: {
      //     userId: ownerId,
      //     ownerType: type
      //   }
      // }
    ] as App.Components.FormAdvance.FormElement[];
  };
