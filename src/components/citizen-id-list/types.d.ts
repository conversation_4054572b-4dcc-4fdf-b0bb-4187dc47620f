declare namespace App.Components.CitizenIdList {
  type FormProps = import("antd").FormProps;
  type ButtonProps = import("antd").ButtonProps;

  export type CitizenIdListProps = {
    info?: App.DataTypes.Staff | App.DataTypes.Worker
    type?: keyof typeof import("@/constants/constants").AttachmentOwnerType;
  };

  export type CardProps = {
    citizenIdentity: App.DataTypes.CitizenIdentityCard;
    onDelete?: (id: string) => void;
    onEdit?: (citizenIdentity: App.DataTypes.CitizenIdentityCard) => void;
  };
}

declare namespace App.Components.CitizenIdentity {
  type FormAntdProps<T> = import("antd").FormProps<T>;

  type ValueType = {
    _id?: string;
    number: string; // số chứng minh nhân dân
    fullname: string; // họ tên
    dob: dayjs.Dayjs; // ngày sinh
    nationality: string; // quốc tịch
    dateOfIssue: dayjs.Dayjs; // ngày cấp
    placeOfIssue: string; // nơi cấp
    dateOfExpiry: dayjs.Dayjs; // ngày hết hạn
    issuedBy: string; // cơ quan cấp
    owner: string;
    gender: string;
    attachments: string[];
    placeOfOrigin: {
      province: string;
      district: string;
      ward: string;
      detail: string;
    };
    placeOfResidence: {
      province: string;
      district: string;
      ward: string;
      detail: string;
    };
  };



  type FormProps = FormAntdProps<ValueType> & {
    value?: ValueType;
    onFinish?: (value: ValueType) => void;
  };

  type FormRef = {
    focus(): void;
    submit(): void;
    reset(): void;
    setValue(data: ValueType): void;
  };

  type ButtonTriggerProps = FormProps & {
    children?: React.ReactNode;
    buttonProps?: ButtonProps;
    modelProps?: import("antd").ModalProps;
    owner: string;
    onSuccess?: () => void;
  };

  type ButtonTriggerRef = {
    show: (data?: ValueType) => void;
    hide: () => void;
    citizenIdentityForm: React.RefObject<FormRef>;
  };
}
