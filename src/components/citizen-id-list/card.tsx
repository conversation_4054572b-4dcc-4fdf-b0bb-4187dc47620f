import React from "react";
import {
  Descriptions,
  notification,
  Space,
  type DescriptionsProps
} from "antd";
import lodash from "lodash";
import { Icon } from "@iconify/react";

import { Button, Typography } from "@/atoms";
import CitizenIdentityForm from "./citizen-identity-form";
import dayjs from "dayjs";
import { GenderMap } from "@/constants";

const InternalCitizenIdentityCard = React.memo(
  (props: App.Components.CitizenIdList.CardProps) => {
    const { citizenIdentity, onDelete, onEdit } = props;

    const [api, contextHolder] = notification.useNotification();

    const showConfirm = React.useCallback(() => {
      const btn = (
        <Space>
          <Button type="primary" size="small" onClick={() => api.destroy()}>
            Hủy
          </Button>
          <Button
            type="text"
            size="small"
            danger
            onClick={() => {
              api.destroy();
              onDelete?.(citizenIdentity._id);
            }}
          >
            Xóa
          </Button>
        </Space>
      );

      api.open({
        message: "Bạn có chắc chắn muốn xóa CCCD này không?",
        description: "Bạn sẽ không thể khôi phục lại sau khi xóa",
        type: "warning",
        btn: btn
      });
    }, [onDelete, citizenIdentity, api]);

    const handleEdit = () => {
      onEdit?.(citizenIdentity);
    };

    const items = React.useMemo<DescriptionsProps["items"]>(
      () => [
        {
          key: "number",
          label: (
            <Typography.Title level={3} className="mb-0">
              Số CCCD
            </Typography.Title>
          ),
          span: 12,
          children: lodash.get(citizenIdentity, "number")
        },
        {
          key: "fullname",
          label: (
            <Typography.Title level={3} className="mb-0">
              Họ và tên
            </Typography.Title>
          ),
          span: 12,
          children: lodash.get(citizenIdentity, "fullname")
        },
        {
          key: "dob",
          label: (
            <Typography.Title level={3} className="mb-0">
              Ngày sinh
            </Typography.Title>
          ),
          span: 4,
          children: citizenIdentity?.dob
            ? dayjs(citizenIdentity.dob).format("DD/MM/YYYY")
            : "?"
        },
        {
          key: "gender",
          label: (
            <Typography.Title level={3} className="mb-0">
              Giới tính
            </Typography.Title>
          ),
          span: 4,
          children: GenderMap.get(
            lodash.get(citizenIdentity, "gender")
          )?.title
        },
        {
          key: "nationality",
          label: (
            <Typography.Title level={3} className="mb-0">
              Quốc tịch
            </Typography.Title>
          ),
          span: 4,
          children: lodash.get(citizenIdentity, "nationality")
        },
        // {
        //   key: 'placeOfOrigin',
        //   <Typography.Title className="">label</Typography.Title>: 'Quê quán',
        //   span: 12,
        //   children: lodash.get(citizenIdentity, 'placeOfOrigin.province.name')
        // },
        // {
        //   key: 'placeOfResidence',
        //   <Typography.Title className="">label</Typography.Title>: 'Nơi thường trú',
        //   span: 12,
        //   children: lodash.get(citizenIdentity, 'placeOfResidence.province.name')
        // },
        {
          key: "issuedBy",
          label: (
            <Typography.Title level={3} className="mb-0">
              Cơ quan cấp
            </Typography.Title>
          ),
          span: 3,
          children: lodash.get(citizenIdentity, "issuedBy")
        },
        {
          key: "placeOfIssue",
          label: (
            <Typography.Title level={3} className="mb-0">
              Nơi cấp
            </Typography.Title>
          ),
          span: 3,
          children: lodash.get(citizenIdentity, "placeOfIssue")
        },
        {
          key: "dateOfIssue",
          label: (
            <Typography.Title level={3} className="mb-0">
              Ngày cấp
            </Typography.Title>
          ),
          span: 3,
          children: citizenIdentity?.dateOfIssue
            ? dayjs(citizenIdentity.dateOfIssue).format("DD/MM/YYYY")
            : "?"
        },
        {
          key: "dateOfExpiry",
          label: (
            <Typography.Title level={3} className="mb-0">
              Có thời hạn tới
            </Typography.Title>
          ),
          span: 3,
          children: citizenIdentity?.dateOfExpiry
            ? dayjs(citizenIdentity.dateOfExpiry).format("MM/YYYY")
            : "?"
        }
        // {
        //   key: 'attachments',
        //   label: <Typography.Title level={3} className="mb-0">Tệp đính kèm</Typography.Title>,
        //   span: 12,
        //   children: (
        //     <div className=''>
        //       {citizenIdentity?.attachments.map(a => (
        //         <div className='inline-block mx-2' key={a._id}>
        //           <Image src={a.url} height={40} className='w-auto'/>
        //         </div>
        //       ))}
        //     </div>
        //   )
        // }
      ],
      [citizenIdentity]
    );

    return (
      <div className="overflow-hidden rounded-lg shadow-default w-full relative group">
        <Descriptions
          items={items}
          layout="vertical"
          bordered
          size="small"
          className="w-full"
          column={12}
        />
        {contextHolder}
        <div className="absolute top-2 right-2 invisible group-hover:visible flex gap-2 py-1 px-2 rounded-full shadow-default bg-white">
          <Button shape="circle" size="small">
            <Icon icon="icon-park-outline:edit" onClick={handleEdit} />
          </Button>
          <Button shape="circle" danger size="small">
            <Icon icon="icon-park-outline:delete" onClick={showConfirm} />
          </Button>
        </div>
      </div>
    );
  }
);

type CitizenIdentityCardType = typeof InternalCitizenIdentityCard & {
  Form: typeof CitizenIdentityForm;
};
const CitizenIdentityCard =
  InternalCitizenIdentityCard as CitizenIdentityCardType;
CitizenIdentityCard.Form = CitizenIdentityForm;

export default CitizenIdentityCard;
