import React from "react";
import { Icon, message } from "@/atoms";
import { <PERSON><PERSON> } from "@/atoms";
import dayjs from "dayjs";
import InfoSection from "../info-section";
import { Gender } from "@/constants";
import {
  citizenIdentityFormElements,
  citizenIdentityFormElementTypes
} from "./form-elements";
import addressElement from "@/components/new-entity-form/address-element";
import CitizenIdentityCard from "./card";
import { CitizenIdentityService, StorageService } from "@/services";
import Uploader from "@/components/uploader";
import { useMutation, useQuery } from "@tanstack/react-query";
import { DATE_FORMAT } from "@/contants";
import { pick } from "lodash";
import FormAdvance from "../form-advance";
import { Form, UploadFile } from "antd";
import { Empty } from "@/components";

const citizenIdentityService = new CitizenIdentityService();
const storageService = new StorageService();

const elementTypes = {
  attachment: Uploader.AttachmentFormItem,
  address: addressElement as unknown as React.ComponentType
};

const CitizenIdList = (
  props: App.Components.CitizenIdList.CitizenIdListProps
) => {
  const { info, type = "USER" } = props;

  const [messageApi, contextHolder] = message.useMessage();

  const owner = storageService.local.load<App.DataTypes.User>("user");

  const [citizenIdentityFormInstance] =
    Form.useForm<App.DataTypes.CitizenIdentityCard>();
  const [citizenIdentityUpdateFormInstance] =
    Form.useForm<App.DataTypes.CitizenIdentityCard>();

  const citizenIdentityFormRef =
    React.useRef<App.Components.FormAdvance.FormAdvanceRef>(null);
  const citizenIdentityUpdateFormRef =
    React.useRef<App.Components.FormAdvance.FormAdvanceRef>(null);

  const { data: citizenIdentityList, refetch } = useQuery({
    queryKey: ["citizen-identity-list", info?.user?._id],
    queryFn: () =>
      citizenIdentityService
        .getCitizenIdentities(info?.user?._id as string)
        .then((res) => res.data?.results),
    enabled: !!info?.user?._id
  });

  const createMutation = useMutation({
    mutationFn: (data: App.DataTypes.CitizenIdentityCard) => {
      console.log(data);
      return citizenIdentityService.createCitizenIdentity(data);
    },
    onSuccess: (res) => {
      if (res.data) {
        message.success("Thêm căn cước công dân thành công");
        citizenIdentityFormRef?.current?.done();
        refetch();
      } else {
        message.error("Thêm căn cước công dân thất bại");
        citizenIdentityFormRef?.current?.fail();
      }
    }
  });

  const updateMutation = useMutation({
    mutationFn: (data: {
      id: string;
      payload: App.Services.CitizenIdentity.ValueType;
    }) => {
      return citizenIdentityService.updateCitizenIdentity(
        data.id,
        data.payload
      );
    },
    onSuccess: (res) => {
      if (res.data) {
        messageApi.open({
          type: "success",
          content: "Cập nhật căn cước công dân thành công"
        });
        citizenIdentityUpdateFormRef?.current?.done();
        refetch();
      } else {
        messageApi.open({
          type: "error",
          content: "Cập nhật căn cước công dân thất bại"
        });
        citizenIdentityUpdateFormRef?.current?.fail();
      }
    }
  });

  const deleteMutation = useMutation({
    mutationFn: (id: string) => {
      return citizenIdentityService.deleteCitizenIdentity(id);
    },
    onSuccess: (res) => {
      if (res.data) {
        messageApi.open({
          type: "success",
          content: "Xoá căn cước công dân thành công"
        });
        refetch();
      } else {
        messageApi.open({
          type: "error",
          content: "Xoá căn cước công dân thất bại"
        });
      }
    }
  });

  const handleCreateFinish: App.Components.FormAdvance.OnFinishEvent<App.DataTypes.CitizenIdentityCard> =
    React.useCallback(
      (info) => {
        const { values, api } = info;
        api.loading();

        const payload = {
          ...values,
          attachments: values.attachments
            ? values.attachments.map((attachment) => attachment._id)
            : [],
          dateOfIssue: dayjs(values.dateOfIssue).format(DATE_FORMAT),
          dateOfExpiry: dayjs(values.dateOfExpiry).format(DATE_FORMAT),
          dob: dayjs(values.dob).format(DATE_FORMAT)
        } as unknown as App.DataTypes.CitizenIdentityCard;

        console.log(payload);

        createMutation.mutate(payload);
      },
      [createMutation]
    );

  const handleUpdateFinish: App.Components.FormAdvance.OnFinishEvent<App.DataTypes.CitizenIdentityCard> =
    React.useCallback(
      (info) => {
        const { values, api } = info;
        api.loading();
        const { _id } = values;
        const payload = {
          ...pick(values, [
            "number",
            "fullname",
            "gender",
            "nationality",
            "placeOfIssue",
            "issuedBy"
            // "attachments"
          ]),
          dateOfIssue: dayjs(values.dateOfIssue).format(DATE_FORMAT),
          dateOfExpiry: dayjs(values.dateOfExpiry).format(DATE_FORMAT),
          dob: dayjs(values.dob).format(DATE_FORMAT),
        } as unknown as App.Services.CitizenIdentity.ValueType;

        updateMutation.mutate({ id: _id, payload });
      },
      [updateMutation]
    );

  const handleShowCitizenIdentityForm = () => {
    citizenIdentityFormRef?.current?.show();
  };

  const handleEditCitizenIdentity = React.useCallback(
    (data: App.DataTypes.CitizenIdentityCard) => {
      citizenIdentityUpdateFormRef?.current?.edit({
        ...data,
        dob: dayjs(data.dob) as unknown as string,
        dateOfIssue: dayjs(data.dateOfIssue) as unknown as string,
        dateOfExpiry: dayjs(data.dateOfExpiry) as unknown as string
      });
    },
    [citizenIdentityUpdateFormRef]
  );

  const handleDeleteCitizenIdentity = React.useCallback<
    NonNullable<App.Components.CitizenIdList.CardProps["onDelete"]>
  >(
    (id: string) => {
      deleteMutation.mutate(id);
    },
    [deleteMutation]
  );

  return (
    <>
      {contextHolder}
      <InfoSection
        id="citizen-identity"
        title="Căn cước công dân"
        icon={<Icon icon="icon-park-outline:bank-card" />}
        renderButtons={() =>
          citizenIdentityList?.length ? (
            <Button onClick={handleShowCitizenIdentityForm}>
              Tạo căn cước công dân mới
            </Button>
          ) : null
        }
      >
        <div className="w-full grid grid-cols-2 gap-5">
          {citizenIdentityList?.length ? (
            citizenIdentityList.map((data) => (
              <div key={data._id} className="col-span-1">
                <CitizenIdentityCard
                  citizenIdentity={data}
                  onDelete={handleDeleteCitizenIdentity}
                  onEdit={handleEditCitizenIdentity}
                />
              </div>
            ))
          ) : (
            <Empty.Section
              className="mt-2 col-span-2"
              description="Chưa có căn cước công dân nào cho nhân viên"
              onCreateNew={handleShowCitizenIdentityForm}
            />
          )}
        </div>
      </InfoSection>
      <FormAdvance
        formKey="citizen-identity-create-form"
        modalProps={{
          title: "Thêm căn cước công dân"
        }}
        elements={citizenIdentityFormElements(owner?._id as string, type)}
        elementTypes={citizenIdentityFormElementTypes}
        initialValues={{
          owner: info?.user?._id,
          fullname: info?.profile?.fullname || info?.user?.name,
          gender: info?.profile?.gender || Gender.MALE,
          dob: dayjs(info?.profile?.dob || dayjs().add(-18, "year")),
          nationality: "Việt Nam"
        }}
        form={citizenIdentityFormInstance}
        ref={citizenIdentityFormRef}
        onFinish={handleCreateFinish}
      />
      <FormAdvance
        formKey="citizen-identity-update-form"
        modalProps={{
          title: "Sửa căn cước công dân"
        }}
        elements={citizenIdentityFormElements(owner?._id as string, type)}
        elementTypes={elementTypes}
        form={citizenIdentityUpdateFormInstance}
        ref={citizenIdentityUpdateFormRef}
        onFinish={handleUpdateFinish}
      />
    </>
  );
};

export default React.memo(CitizenIdList);
