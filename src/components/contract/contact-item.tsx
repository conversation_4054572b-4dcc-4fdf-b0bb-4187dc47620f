import React from "react";
import { Button } from "@/atoms";
import { Descriptions, DescriptionsProps, notification, Space } from "antd";
import dayjs from "dayjs";
import { StaffContractStatussMap, StaffContractTypesMap } from "@/constants";
import lodash from "lodash";
import { Icon } from "@iconify/react/dist/iconify.js";

const noContent = <div className="text-center text-gray-500">Không có dữ liệu</div>;

const ContractItem = (props: App.Components.Contract.ContractItemProps) => {
  const { contract, onEdit, onDelete } = props;

  const [api, contextHolder] = notification.useNotification();

  const items = [
    {
      key: "title",
      label: "Tiêu đề:",
      span: 2,
      children: lodash.get(contract, "title") || noContent
    },
    {
      key: "type",
      label: "<PERSON><PERSON>i hợp đồng:",
      span: "filled",
      children:
        StaffContractTypesMap.get(lodash.get(contract, "type"))?.title || noContent
    },
    {
      key: "contractNumber",
      label: "<PERSON><PERSON> hợp đồng:",
      children: lodash.get(contract, "contractNumber") || noContent
    },
    {
      key: "status",
      label: "Trạng thái ký kết:",
      children:
        StaffContractStatussMap.get(lodash.get(contract, "status"))?.title ||
        noContent
    },
    {
      key: "startDate",
      label: "Có hiệu lực từ:",
      children:
        dayjs(lodash.get(contract, "startDate")).format("DD/MM/YYYY") || noContent
    },
    {
      key: "endDate",
      label: "Ngày hết hạn:",
      children:
        dayjs(lodash.get(contract, "endDate")).format("DD/MM/YYYY") || noContent
    },
    {
      key: "signingDate",
      label: "Ngày ký hợp đồng:",
      children:
        dayjs(lodash.get(contract, "signingDate")).format("DD/MM/YYYY") ||
        noContent
      // children: lodash.get(contract, "signingDate") || noContent
    },
    {
      key: "salary",
      label: "Mức lương:",
      children: lodash.get(contract, "salary") || noContent
    },
    {
      key: "signedBy",
      label: "Người ký:",
      children: lodash.get(contract, "signedBy.user.name") || noContent
    },
    {
      key: "attachments",
      label: "Tài liệu đính kèm:",
      children: lodash
        .get(contract, "attachments")
        .map((a) => a.title)
        .join(",")
    },
    {
      key: "description",
      label: "Mô tả:",
      span: "filled",
      children: lodash.get(contract, "description") || "?"
    }
  ];

  const handleEdit = React.useCallback(() => {
    onEdit?.(contract);
  }, [onEdit, contract]);

  const showConfirm = React.useCallback(() => {
    const btn = (
      <Space>
        <Button type="primary" size="small" onClick={() => api.destroy()}>
          Hủy
        </Button>
        <Button
          type="text"
          size="small"
          danger
          onClick={() => {
            api.destroy();
            onDelete?.(contract._id);
          }}
        >
          Xóa
        </Button>
      </Space>
    );

    api.open({
      message: "Bạn có chắc chắn muốn xóa bảo hiểm này không?",
      description: "Bạn sẽ không thể khôi phục lại sau khi xóa",
      type: "warning",
      btn: btn
    });
  }, [api, onDelete, contract]);

  return (
    <div className="overflow-hidden rounded-lg shadow-default w-full relative group mb-4">
      {contextHolder}
      <Descriptions
        items={items as DescriptionsProps["items"]}
        layout="vertical"
        bordered
        size="small"
        className="w-full"
      />

      <div className="absolute top-2 right-2 invisible group-hover:visible flex gap-2 py-1 px-2 rounded-full shadow-default bg-white">
        <Button shape="circle" size="small" onClick={handleEdit}>
          <Icon icon="icon-park-outline:edit" />
        </Button>
        <Button shape="circle" danger size="small" onClick={showConfirm}>
          <Icon icon="icon-park-outline:delete" />
        </Button>
      </div>
    </div>
  );
};

export default React.memo(ContractItem);
