declare namespace App.Components.Contract {
  type ContractDataType = {
    id: string
    name: string
    contractId: string
    type: string
    status: string
    startDate: Date
    endDate: Date
    signDate: Date
    salary: number
    attachments: string[]
  }

  export interface Props {
    data: ContractDataType[]
  }

  export interface ContractItemProps {
    contract: App.DataTypes.StaffContract
    onEdit?: (contract: App.DataTypes.StaffContract) => void
    onDelete?: (id: string) => void
  }
}

