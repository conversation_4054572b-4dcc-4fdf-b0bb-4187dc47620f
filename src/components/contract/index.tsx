import React from "react";
import ContractItem from "./contact-item";
import { Typography } from "@/atoms";

const Contract = React.forwardRef((props: App.Components.Contract.Props) => {
  const { data } = props;
  return (
    <div>
      {data.map((item, index) => (
        <div key={item.id}>
          <Typography.Text className="text-base font-semibold">{`${
            index + 1
          }. ${item.name} - ${item.contractId}`}</Typography.Text>
          <ContractItem data={item} />
        </div>
      ))}
    </div>
  );
});

export default Contract;
