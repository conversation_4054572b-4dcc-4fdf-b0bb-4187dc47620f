import React from "react";
import BankItem from "./bank-account";
import "./styles.css";
import { PageContentHeader } from "../page-content";
import { Icon } from "@iconify/react";
import { TabsMap, Tabs } from "@/constants/tabs";
import { useQuery, useMutation } from "@tanstack/react-query";
import BanksService from "@/services/banks";
import FormAdvance from "../form-advance";
import { bankFormElements, elementTypes } from "./form-elements";
import { Form, message } from "antd";
import { omit } from "lodash";
import { Empty } from "@/components";
import { Button } from "@/atoms";

const bankService = new BanksService();

const headerIcon = TabsMap.get(Tabs.BANK_ACCOUNTS)?.icon as string;
const BankAccounts = (props: App.Components.Banks.Props) => {
  const { info } = props;

  const ownerId = info?.user?._id;

  const [form] = Form.useForm<App.DataTypes.BankAccount>();
  const formRef = React.useRef<App.Components.FormAdvance.FormAdvanceRef>(null);
  const [formEdit] = Form.useForm<App.DataTypes.BankAccount>();
  const formEditRef =
    React.useRef<App.Components.FormAdvance.FormAdvanceRef>(null);

  const { data, refetch } = useQuery({
    queryKey: ["bank-accounts", ownerId],
    queryFn: () =>
      bankService
        .getBankAccounts({ owner: ownerId })
        .then((res) => res.data?.results),
    enabled: !!ownerId
  });

  const { mutate: createBankAccount } = useMutation({
    mutationFn: (bankAccount: App.DataTypes.BankAccount) =>
      bankService.createBankAccount(bankAccount),
    onSuccess: (res) => {
      if (res?.data) {
        message.success("Thêm tài khoản ngân hàng thành công");
        formRef.current?.done();
        refetch();
      } else {
        message.error("Thêm tài khoản ngân hàng thất bại");
        formRef.current?.fail();
      }
    }
  });

  const { mutate: updateBankAccount } = useMutation({
    mutationFn: (payload: App.DataTypes.BankAccount) =>
      bankService.updateBankAccount(
        payload._id,
        omit(payload, "_id") as App.DataTypes.BankAccount
      ),
    onSuccess: (res) => {
      if (res?.data) {
        message.success("Cập nhật tài khoản ngân hàng thành công");
        formEditRef.current?.done();
        refetch();
      } else {
        message.error("Cập nhật tài khoản ngân hàng thất bại");
        formEditRef.current?.fail();
      }
    }
  });

  const { mutate: deleteBankAccount } = useMutation({
    mutationFn: (id: string) => bankService.deleteBankAccount(id),
    onSuccess: (res) => {
      if (res?.data) {
        message.success("Xóa tài khoản ngân hàng thành công");
        refetch();
      } else {
        message.error("Xóa tài khoản ngân hàng thất bại");
      }
    }
  });

  const handleCreateBankFinish: App.Components.FormAdvance.OnFinishEvent<App.DataTypes.BankAccount> =
    React.useCallback(
      (info) => {
        const { values, api } = info;
        api.loading();
        const payload = {
          ...values,
          owner: ownerId
        };
        createBankAccount(payload);
      },
      [createBankAccount, ownerId]
    );

  const handleOpenForm = React.useCallback(() => {
    formRef.current?.show();
  }, []);

  const handleUpdateBankFinish: App.Components.FormAdvance.OnFinishEvent<App.DataTypes.BankAccount> =
    React.useCallback(
      (info) => {
        const { values, api } = info;
        api.loading();
        const payload = {
          ...values,
          owner: ownerId
        };
        updateBankAccount(payload);
      },
      [updateBankAccount, ownerId]
    );

  const handleDeleteBankAccount = React.useCallback(
    (info: { _id: string }) => {
      deleteBankAccount(info._id);
    },
    [deleteBankAccount]
  );

  const showUpdateForm = React.useCallback(
    (info: App.DataTypes.BankAccount) => {
      formEditRef.current?.show();
      formEdit.setFieldsValue(info);
    },
    [formEdit]
  );

  return (
    <div className="h-full pb-4">
      <PageContentHeader
        title="Danh sách tài khoản ngân hàng"
        icon={headerIcon}
        subtitle="Quản lý các tài khoản ngân hàng của nhân viên."
        actions={
          data?.length ? (
            <div className="flex gap-2">
              <Button onClick={handleOpenForm}>
                <div className="center gap-1">
                  <Icon icon="icon-park-outline:add" />
                  <span>Thêm tài khoản ngân hàng</span>
                </div>
              </Button>
            </div>
          ) : null
        }
      />

      {data && (
        <div className="flex gap-6 flex-wrap pb-10">
          {data?.length ? (
            data.map((item, index) => (
              <BankItem
                data={item}
                key={`bank-${index}`}
                onDelete={handleDeleteBankAccount}
                onEdit={showUpdateForm}
              />
            ))
          ) : (
            <Empty.Section
              className="mt-2 col-span-2"
              description="Chưa có tài khoản ngân hàng nào cho nhân viên"
              onCreateNew={handleOpenForm}
            />
          )}
        </div>
      )}
      <FormAdvance
        key="create-bank-account"
        modalProps={{
          title: "Thêm tài khoản ngân hàng"
        }}
        form={form}
        ref={formRef}
        elements={bankFormElements}
        elementTypes={elementTypes}
        onFinish={handleCreateBankFinish}
      />
      <FormAdvance
        key="update-bank-account"
        modalProps={{
          title: "Chỉnh sửa tài khoản ngân hàng"
        }}
        form={formEdit}
        ref={formEditRef}
        elements={bankFormElements}
        elementTypes={elementTypes}
        onFinish={handleUpdateBankFinish}
      />
    </div>
  );
};

export default React.memo(BankAccounts);
