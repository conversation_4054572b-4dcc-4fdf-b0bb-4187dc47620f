declare namespace App.Components.Banks {
  export type BankAccountDataType = {
    id: string
    accountName: string
    bankName: string
    accountNumber: string
    bankBranch: string
    swiftCode: string
    isPrimary: boolean
    description: string
  }

  export type Props = {
    info?: App.DataTypes.Staff | App.DataTypes.Worker | App.DataTypes.Vendor
  }

  export type BankAccountProps = {
    data: App.DataTypes.BankAccount
    onEdit?: (info: App.DataTypes.BankAccount) => void
    onDelete?: (info: { _id: string }) => void
  }

}
