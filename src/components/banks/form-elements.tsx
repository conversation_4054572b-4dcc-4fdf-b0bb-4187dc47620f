import BankSelect from "../new-entity-form/bank-select";

export const elementTypes = {
  bankSelect: BankSelect
} as unknown as App.Components.FormAdvance.DynamicRendererProps<App.DataTypes.BankAccount>["elementTypes"];

export const bankFormElements: App.Components.FormAdvance.FormElement[] = [
  {
    name: "_id",
    hidden: true
  },
  {
    name: "accountName",
    label: "Tên tài khoản",
    type: "input",
    rules: [{ required: true, message: "Vui lòng nhập tên tài khoản" }],
    inputProps: {
      placeholder: "Nhập tên tài khoản"
    }
  },
  {
    name: "accountNumber",
    label: "Số tài khoản",
    type: "input",
    rules: [{ required: true, message: "Vui lòng nhập số tài khoản" }],
    inputProps: {
      placeholder: "Nhập số tài khoản"
    }
  },
  {
    type: "bankSelect",
    rules: [{ required: true, message: "<PERSON>ui lòng chọn ngân hàng" }],
    inputProps: {
      placeholder: "Chọn ngân hàng"
    }
  },
  {
    name: "bankBranch",
    label: "<PERSON> nhánh",
    type: "input",
    inputProps: {
      placeholder: "Nhập chi nhánh"
    }
  },
  {
    name: "description",
    label: "Mô tả",
    type: "textarea",
    className: "col-span-2",
    inputProps: {
      rows: 4,
      placeholder: "Nhập mô tả"
    }
  },
  {
    name: "isPrimary",
    label: "Đặt làm thẻ chính",
    type: "checkbox",
    valuePropName: "checked"
  }
];
