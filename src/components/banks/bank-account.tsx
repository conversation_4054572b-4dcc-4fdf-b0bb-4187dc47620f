import React from "react";
import { Icon } from "@iconify/react";
import { QRPay, BanksObject, BankKey } from "vietnam-qr-pay";
import { Button, Modal, notification, QRCode, Space } from "antd";

import { Typography } from "@/atoms";

import { cn } from "@/utils";

const BankAccount = (props: App.Components.Banks.BankAccountProps) => {
  const { data, onDelete, onEdit } = props;

  const [api, contextHolder] = notification.useNotification();  

  const [open, setOpen] = React.useState(false);
  const bankName = data?.bankName.toLocaleLowerCase() ?? "";

  const qrPay = QRPay.initVietQR({
    bankBin: BanksObject[bankName as BankKey]?.bin,
    bankNumber: data?.accountNumber as string
  });

  const content = qrPay.build();
  
  const handleDelete = React.useCallback(() => {
    const btn = (
      <Space>
        <Button type="primary" size="small" onClick={() => api.destroy()}>
          Hủy
        </Button>
        <Button type="text" size="small" danger onClick={() => {
          onDelete?.({ _id: data?._id });
          api.destroy();
        }}>
          Xóa
        </Button>
      </Space>
    );

    api.open({
      message: "Bạn có chắc chắn muốn xóa tài khoản ngân hàng này không?",
      description: "Bạn sẽ không thể khôi phục lại sau khi xóa",
      type: "warning",
      duration: 0,
      btn: btn
    });
  }, [onDelete, data, api]);

  const handleEdit = React.useCallback(() => {
    onEdit?.(data);
  }, [onEdit, data]);

  return (
    <div className="group cursor-pointer shadow-md w-full max-w-[340px] h-auto aspect-[17/10] rounded-2xl transition-shadow duration-300 hover:shadow-lg  bg-gradient-to-br from-gray-200 to-blue-200 p-6 relative flex flex-col justify-between">
      {contextHolder}
      <div className="flex flex-col">
        <Typography.Text className="mb-0 text-2xl font-bold">
          {data?.bankName}
        </Typography.Text>
        {data?.isPrimary && (
          <Typography.Text className="text-gray-500">Mặc định</Typography.Text>
        )}
      </div>
      <Typography.Text className="mb-0 text-xl tracking-widest font-semibold mt-auto">
        {data?.accountNumber}
      </Typography.Text>

      <div className="">
        <div className="flex flex-col mt-3">
          <Typography.Text className="mb-0 text-sm font-semibold">
            {data?.accountName}
          </Typography.Text>
          <Typography.Text className="mb-0 text-xs tracking-wide">
            {data?.bankBranch}
          </Typography.Text>
        </div>
        <Button
          ghost
          icon={
            <Icon
              icon="icon-park-outline:edit"
              width={18}
              height={18}
            />
          }
          className={cn(
            "opacity-0 ease-in-out duration-100 group-hover:opacity-100",
            "absolute bottom-3 right-10",
            "border-0 p-0"
          )}
          onClick={handleEdit}
        />
        <Button
          danger
          ghost
          icon={
            <Icon
              icon="icon-park-outline:delete-themes"
              width={18}
              height={18}
            />
          }
          className={cn(
            "opacity-0 ease-in-out duration-100 group-hover:opacity-100",
            "absolute bottom-3 right-3",
            "border-0 p-0"
          )}
          onClick={handleDelete}
        />
      </div>

      <div className="absolute top-6 right-6 flex flex-col gap-2">
        <Button size="small" icon={<Icon icon="f7:qrcode" />} onClick={() => setOpen(true)} />
      </div>
      <Modal
        open={open}
        onCancel={() => setOpen(false)}
        footer={null}
        title={`Mã QR cho ${data?.accountName || data?.bankName}`}
        width={'fit-content'}
      >
        <QRCode value={content} size={400} className="bg-white" />
      </Modal>
    </div>
  );
};

BankAccount.displayName = "BankAccount";
export default React.memo(BankAccount);
