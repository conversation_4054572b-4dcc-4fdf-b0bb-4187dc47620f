import React from "react";
import SmallCalendar from "../small-calendar";

const Sidebar = React.forwardRef<
  App.Components.Calendar.SidebarRef,
  App.Components.Calendar.SidebarProps
>((_props, ref) => {
  const { onChange } = _props;
  const [date, setDate] = React.useState(new Date());

  React.useImperativeHandle(
    ref,
    () => ({
      setDate(date) {
        setDate(date);
      }
    }),
    []
  );

  return <SmallCalendar currentDate={date} onChange={onChange} />;
});

Sidebar.displayName = "Sidebar";

export default React.memo(Sidebar);
