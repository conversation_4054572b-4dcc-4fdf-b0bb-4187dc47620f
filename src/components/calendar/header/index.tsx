import React from "react";
import { <PERSON><PERSON>, Typography } from "@/atoms";
import { Icon } from "@iconify/react";
import { Dropdown, Popover } from "antd";
import dayjs from "dayjs";
import SmallCalendar from "../small-calendar";
import "./index.css";
import { EVENT_TIME_OPTIONS } from "./contants";

const { Text } = Typography;

const Header = React.forwardRef<
  App.Components.Calendar.HeaderCalendarRef,
  App.Components.Calendar.HeaderCalendarProps
>((props, ref) => {
  const {
    timeOptions = EVENT_TIME_OPTIONS,
    resourceOptions,
    onDateChange,
    onTodayClick,
    onNextClick, 
    onTimeViewModeChange,
    onPrevClick,
    onResourceViewModeChange
  } = props;
  const [currentDate, setCurrentDate] = React.useState(new Date());
  const [currentTimeOption, setCurrentTimeOption] = React.useState(
    timeOptions[0]
  );
  const [currentResourceOption, setCurrentResourceOption] = React.useState(
    resourceOptions ? resourceOptions[0] : {}
  );

  const [open, setOpen] = React.useState(false);

  React.useImperativeHandle(
    ref,
    () => ({
      setDate: (newDate) => {
        setCurrentDate(newDate);
      }
    }),
    []
  );

  const timeDropdownClick = React.useCallback(
    ({ key }: { key: string }) => {
      const optionIndex = timeOptions.findIndex(
        (option: { key: string }) => option.key === key
      );

      setCurrentTimeOption(timeOptions[optionIndex]);

      onTimeViewModeChange?.(key);
    },
    [currentTimeOption, onTimeViewModeChange]
  );

  const resourceDropdownClick = React.useCallback(
    ({ key }: { key: string }) => {
      const optionIndex = resourceOptions.findIndex(
        (option: { key: string }) => option.key === key
      );

      setCurrentResourceOption(resourceOptions[optionIndex]);

      onResourceViewModeChange?.(key);
    },
    [currentResourceOption, onResourceViewModeChange]
  );

  const dateChange = React.useCallback(
    (date: Date) => {
      setOpen(false);
      setCurrentDate(date);
      onDateChange?.(date);
    },
    [onDateChange]
  );

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
  };

  return (
    <div className="flex gap-4 items-center p-3">
      <div>
        <Popover
          content={
            <SmallCalendar currentDate={currentDate} onChange={dateChange} />
          }
          open={open}
          onOpenChange={handleOpenChange}
          title={"Jump to date"}
          trigger={["click"]}
        >
          <Text className="capitalize flex gap-2 items-center select-none cursor-pointer text-base font-semibold">
            {dayjs(currentDate).format("MMMM YYYY")}
            <Icon icon="icon-park-outline:down" />
          </Text>
        </Popover>
      </div>
      <div className="flex gap-1">
        <Button className="px-2 text-base" onClick={onPrevClick}>
          <Icon icon="icon-park-outline:left" />
        </Button>
        <Button className="font-semibold" onClick={onTodayClick}>
          Today
        </Button>
        <Button className="px-2 text-base" onClick={onNextClick}>
          <Icon icon="icon-park-outline:right" />
        </Button>
      </div>
      <div className="text-base font-semibold">
        <Dropdown
          trigger={["click"]}
          menu={{
            items: timeOptions,
            onClick: timeDropdownClick,
            selectedKeys: [currentTimeOption.key]
          }}
          destroyPopupOnHide
        >
          <Button>
            <Text className="flex items-center gap-1 text-sm font-semibold">
              {currentTimeOption.label}
              <Icon className="text-base" icon="icon-park-outline:down" />
            </Text>
          </Button>
        </Dropdown>
      </div>
      {resourceOptions && (
        <div className="text-base font-semibold">
          <Dropdown
            trigger={["click"]}
            menu={{
              items: resourceOptions,
              onClick: resourceDropdownClick,
              selectedKeys: [currentResourceOption.key]
            }}
            destroyPopupOnHide
          >
            <Button>
              <Text className="flex items-center gap-1 text-sm font-semibold">
                {currentResourceOption.label}
                <Icon className="text-base" icon="icon-park-outline:down" />
              </Text>
            </Button>
          </Dropdown>
        </div>
      )}
    </div>
  );
});

export default React.memo(Header);
