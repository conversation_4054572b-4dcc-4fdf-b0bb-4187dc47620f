declare namespace App.Components.Calendar {
  export type HeaderCalendarProps = {
    timeOptions?: Atoms.DropDownProps["options"]
    resourceOptions?: Atoms.DropDownProps["options"]
    onTodayClick?: () => void;
    onNextClick?: () => void;
    onPrevClick?: () => void;
    onDateChange?: (date: Date) => void;
    onTimeViewModeChange?: (key: string) => void;
    onResourceViewModeChange?: (key: string) => void;
  }
  export type HeaderCalendarRef = {
    setDate: (newDate: Date) => void;
  }
}
