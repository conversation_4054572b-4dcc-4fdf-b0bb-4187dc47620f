export const TimeLineTimeOptions: Atoms.DropDownProps["options"] = [
  {
    label: "Year",
    key: "customResourceTimelineYear"
  },
  {
    label: "Quarter",
    key: "customResourceTimelineQuarter"
  },
  {
    label: "Month",
    key: "customResourceTimelineMonth"
  },
  {
    label: "Week",
    key: "custonResourceTimelineWeek"
  },
  {
    label: "Day",
    key: "customResourceTimelineDay"
  }
];

export const TimelineResourceOptions: Atoms.DropDownProps["options"] = [
  {
    label: "List",
    key: "list"
  },
  {
    label: "Member",
    key: "member"
  },
  {
    label: "Label",
    key: "label"
  },
  {
    label: "None",
    key: "none"
  }
];

export const EVENT_TIME_OPTIONS: Atoms.DropDownProps["options"] = [
  {
    label: "Month",
    key: "dayGridMonth"
  },
  {
    label: "Week",
    key: "dayGridWeek"
  },
  {
    label: "Day",
    key: "dayGridDay"
  }
];