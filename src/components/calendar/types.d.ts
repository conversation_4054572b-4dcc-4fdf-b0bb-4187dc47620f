declare namespace App.Components.Calendar {
  import type { default as FullCalendar } from '@fullcalendar/react'
  type FullCalendarProps = React.ComponentProps<typeof import('@fullcalendar/react').default>

  export interface Props
    extends FullCalendarProps {
    timeOptions?: Atoms.DropDownProps["options"]
    resourceOptions?: Atoms.DropDownProps["options"]
    className?: string;
    onTimeViewModeChange?: (type: string) => void
    onResourceViewModeChange?: (type: string) => void
    onEventClick?: (id: string) => void;
    onToggeEventComplete?: (info: { id: string; complete: boolean }) => void;
  }

  export type Ref = Pick<FullCalendar, 'getApi'> & {
    
  }

  type EventContentArg = import('@fullcalendar/core').EventContentArg
  export interface EventContentProps extends EventContentArg {
    onToggeEventComplete?: (info: { id: string; complete: boolean }) => void;
  }
}
