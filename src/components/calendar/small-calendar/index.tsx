import React from "react";
import { Calendar, ConfigProvider } from "antd";
import dayjs, { Dayjs } from "dayjs";
import "dayjs/locale/vi";
import locale from "antd/locale/vi_VN";
import { Button, Typography } from "@/atoms";
import { Icon } from "@iconify/react";
import { HeaderRender, SelectInfo } from "antd/es/calendar/generateCalendar";
import './index.css'

dayjs.locale("vi");

const { Text } = Typography;

const SmallCalendar = (props: App.Components.Calendar.SmallCalendarProps) => {
  const { currentDate, onChange } = props;

  const [date, setDate] = React.useState(dayjs(currentDate));

  const dateChange = React.useCallback(
    (date: Dayjs, info: SelectInfo) => {
      const { source } = info;

      setDate(date);

      if (source === "customize") return;

      onChange(date.toDate());
    },
    [date, currentDate, onChange]
  );

  return (
    <ConfigProvider locale={locale}>
      <div className="flex max-w-80">
        <Calendar
          value={date}
          headerRender={(props) => <SmallCalendarHeader {...props} />}
          // onChange={}
          onSelect={dateChange}
          fullscreen={false}
        />
      </div>
    </ConfigProvider>
  );
};

const SmallCalendarHeader: HeaderRender<Dayjs> = (props) => {
  const { value, onChange } = props;

  const onPrevClick = React.useCallback(() => {
    onChange(value.add(-1, "month"));
  }, [value]);

  const onNextClick = React.useCallback(() => {
    onChange(value.add(1, "month"));
  }, [value]);

  return (
    <div className="flex justify-between items-center">
      <Button type={'text'} onClick={onPrevClick}>
        <Icon className="text-base" icon="icon-park-outline:left" />
      </Button>
      <Text className="text-base capitalize font-bold">{value.format("MMMM YYYY")}</Text>
      <Button type={'text'} onClick={onNextClick}>
        <Icon className="text-base" icon="icon-park-outline:right" />
      </Button>
    </div>
  );
};

export default SmallCalendar;
