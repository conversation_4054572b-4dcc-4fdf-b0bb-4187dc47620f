// /* eslint-disable custom-rules/encourage-object-params */
import "./calendar.css";

import React from "react";

import type {
  CalendarApi,
  DatesSetArg,
  EventClickArg,
  SlotLabelContentArg
} from "@fullcalendar/core/index.js";
import {
  default as ReactFullCalendar
} from "@fullcalendar/react";
import { Icon } from "@iconify/react";
import vi from "@fullcalendar/core/locales/vi";

import { Typography } from "@/atoms";
import { cn } from "@/utils";
import Header from "./header";
import Sidebar from "./sidebar";
import dayjs from "dayjs";

const { Text } = Typography;

export const Calendar = React.forwardRef<
  App.Components.Calendar.Ref,
  App.Components.Calendar.Props
>(
  (
    {
      className,
      timeOptions,
      resourceOptions,
      dayMaxEventRows = false,
      eventResizableFromStart = true,
      stickyHeaderDates = true,
      onTimeViewModeChange,
      onResourceViewModeChange,
      onEventClick,
      onToggeEventComplete,
      ...props
    },
    ref
  ) =>
    // ref
    {
      const calendarRef = React.useRef<ReactFullCalendar>(null);
      const headerRef =
        React.useRef<App.Components.Calendar.HeaderCalendarRef>(null);
      const sidebarRef = React.useRef<App.Components.Calendar.SidebarRef>(null);

      const [calendarMode, setCalendarMode] = React.useState("dayGridMonth");

      const handleEventClick = React.useCallback((info: EventClickArg) => {
        const { event } = info;
        console.log(info);

        const { id } = event;

        if (!id) return;
        onEventClick?.(info.event.id);
      }, []);

      const nextClick = React.useCallback(() => {
        calendarRef.current?.getApi().next();
      }, []);
      const prevClick = React.useCallback(() => {
        calendarRef.current?.getApi().prev();
      }, []);
      const todayClick = React.useCallback(() => {
        calendarRef.current?.getApi().today();
      }, []);
      const modeChange = React.useCallback((key: string) => {
        setCalendarMode(key);
        calendarRef.current?.getApi().changeView(key);
      }, []);

      const headerDateChange = React.useCallback((date: Date) => {
        calendarRef.current?.getApi().gotoDate(date);
      }, []);

      const calendarDateChange = React.useCallback((dateInfo: DatesSetArg) => {
        const { start, end } = dateInfo;

        const dayInMiddle = new Date((start.getTime() + end.getTime()) / 2);
        headerRef.current?.setDate(dayInMiddle);
      }, []);

      React.useImperativeHandle(
        ref,
        () => ({
          getApi: () => calendarRef.current?.getApi() as CalendarApi
        }),
        [calendarRef]
      );

      return (
        <div
          className={cn("flex flex-col absolute inset-0 p-2", className)}
        >
          <Header
            timeOptions={timeOptions}
            resourceOptions={resourceOptions}
            onTimeViewModeChange={onTimeViewModeChange || modeChange}
            onResourceViewModeChange={onResourceViewModeChange}
            onNextClick={nextClick}
            onPrevClick={prevClick}
            onTodayClick={todayClick}
            onDateChange={headerDateChange}
            ref={headerRef}
          />
          <div className="flex h-full overflow-y-scroll">
            {calendarMode === "dayGridDay" && (
              <Sidebar ref={sidebarRef} onChange={headerDateChange} />
            )}
            <div className="flex-grow">
              <ReactFullCalendar
                ref={calendarRef}
                timeZone="local"
                locale={vi}
                headerToolbar={false}
                eventResizableFromStart={eventResizableFromStart}
                stickyHeaderDates={stickyHeaderDates}
                eventClick={handleEventClick}
                dayMaxEventRows={dayMaxEventRows}
                datesSet={calendarDateChange}
                firstDay={7}
                contentHeight={'auto'}
                dayHeaderContent={CustomSlotContent}
                schedulerLicenseKey="CC-Attribution-NonCommercial-NoDerivatives"
                eventContent={(info) => (
                  <EventContent
                    onToggeEventComplete={onToggeEventComplete}
                    {...info}
                  />
                )}
                {...props}
              />
            </div>
          </div>
        </div>
      );
    }
);
Calendar.displayName = "Calendar";

export default Calendar;

const EventContent = (props: App.Components.Calendar.EventContentProps) => {
  const { event, onToggeEventComplete } = props;

  const {
    id,
    extendedProps: { complete }
  } = event;

  const [isComplete, setIsComplete] = React.useState<boolean>(
    complete || false
  );

  const toggleComplete = React.useCallback(
    (e: React.MouseEvent<HTMLSpanElement>) => {
      e.stopPropagation();
      setIsComplete((prev) => !prev);

      if (!id) return;

      onToggeEventComplete?.({ id, complete: !isComplete });
    },
    [isComplete]
  );

  return (
    <div className="group flex justify-start gap-1 items-center">
      <span
        onClick={toggleComplete}
        className={cn(
          "flex items-center justify-center opacity-0 max-w-0 overflow-hidden group-hover:opacity-90 group-hover:max-w-4 transition-all duration-300",
          isComplete ? "opacity-1 h-4 w-4 max-w-4 rounded-full " : ""
        )}
      >
        {isComplete ? (
          <Icon
            icon="icon-park-solid:check-one"
            className="text-base"
            style={{ color: "#4bce97" }}
          />
        ) : (
          <Icon
            icon="icon-park-outline:round"
            className="opacity-0 group-hover:opacity-100 text-base"
            style={{ color: "#000" }}
          />
        )}
      </span>
      <Text className="text-sm">{event.title}</Text>
    </div>
  );
};

const CustomSlotContent = (props: SlotLabelContentArg) => {
  const { view, date, text } = props;
  const { type } = view;

  let slotContent = null;

  switch (type) {
    case "dayGridWeek":
    case "dayGridMonth":
      slotContent = (
        <span className="flex gap-1">
          <Text className="capitalize">{dayjs(date).format("dddd")}</Text>
          <Text>{dayjs(date).format("DD")}</Text>
        </span>
      );
      break;
    default:
      slotContent = text;
      break;
  }

  return slotContent;
};
