import React from 'react'
import { isString } from 'lodash'

import { Avatar as Avatar<PERSON>tom, Typography } from '@/atoms'
import { cn, getAvatarDisplayName, randomColor } from '@/utils'

import './style.css'

const Avatar = React.forwardRef<
  App.Components.Avatar.Ref,
  App.Components.Avatar.Props
>((props, ref) => {
  const { avatar, avatarDisplayName, className, bgColor, title, subtitle, size = 'default', ...restProps } = props

  React.useImperativeHandle(ref, () => ({}), [])

  const style = React.useMemo<React.CSSProperties>(() => ({
    backgroundColor: bgColor || randomColor()
  }), [bgColor])

  let textLevel: Atoms.AntdTitleProps['level']  = 3
  if (size === 'large') textLevel = 1
  if (size === 'small') textLevel = 5

  let avatarSize = 24;
  if (size === 'large') avatarSize = size;
  if (size === 'small') avatarSize = 20;

  return (
    <div className={cn('flex items-center', size === 'large' ? 'gap-3' : 'gap-2', className)} {...restProps}>
      <AvatarAtom
        className='custom-avatar'
        src={avatar}
        size={avatarSize}
        style={style}
      >
        {avatarDisplayName ? getAvatarDisplayName(avatarDisplayName) : 'MS'}
      </AvatarAtom>
      {title && (
        <div>
          {isString(title) ? (
            <Typography.Text className='mb-0'>
              {title}
            </Typography.Text>
          ) : (
            title
          )}
          {subtitle && (
            <Typography.Text type='subtitle'>{subtitle}</Typography.Text>
          )}
        </div>
      )}
    </div>
  )
})

Avatar.displayName = 'Avatar'

export default React.memo(Avatar)
