import React from 'react';

import InternalAvatar from './internal';

const DepartmentAvatar = React.memo((
  props: { department?: App.DataTypes.Department } & App.Components.Avatar.Props
) => {
  const { department, ...restProps } = props;

  if (!department) return <></>;

  return (
    <InternalAvatar avatar={department.avatar} title={department.name} size='small' {...restProps} />
  )
})

export default DepartmentAvatar;
