import React from "react";
import { Form, Input } from "antd";
import { Button } from "@/atoms";
import { Icon } from "@iconify/react/dist/iconify.js";
import { cn } from "@/utils";

const StringList = (props: App.Components.CustomFormElements.StringList.StringListProps) => {
  const {
    className,
    label = "Liên kết",
    name = "names",
    cols = 1,
    rules,
  } = props;

  console.log("names");
  console.log(props);

  return (
    <Form.Item
      label={label as string}
      className={cn("mb-0", className as string)}
      style={{ gridColumn: `span ${cols} / span ${cols}` }}
    >
      <Form.List name={name as string}>
        {(fields, { add, remove }) => (
          <>
            {fields.map((field) => {
              console.log(field);
              return (
                <div className="flex gap-2" key={field.key}>
                  <Form.Item
                    className="flex-1"
                    {...field}
                    rules={rules}
                  >
                    <Input />
                  </Form.Item>
                  <Button
                    type="text"
                    onClick={() => remove(field.name)}
                    icon={<Icon icon="icon-park-outline:minus" />}
                  />
                </div>
              );
            })}
            <Form.Item>
              <Button
                type="dashed"
                onClick={() => add()}
                block
                icon={<Icon icon="icon-park-outline:plus" />}
              >
                Thêm liên kết
              </Button>
            </Form.Item>
          </>
        )}
      </Form.List>
    </Form.Item>
  );
};

export default React.memo(StringList);
