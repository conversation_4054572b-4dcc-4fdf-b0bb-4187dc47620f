export const changePasswordFormElements: App.Components.FormAdvance.FormElement[] =
  [
    {
      label: "Mật khẩu mới",
      name: "newPassword",
      type: "password",
      className: "col-span-2",
      rules: [{ required: true, message: "<PERSON>ật khẩu mới là bắt buộc" }],
      inputProps: {
        placeholder: "Nhập mật khẩu mới"
      }
    },
    {
      label: "Xác nhận mật khẩu",
      name: "confirmPassword",
      type: "password",
      dependencies: ["newPassword"],
      className: "col-span-2",
      rules: [
        {
          required: true,
          message: "<PERSON><PERSON>c nhận mật khẩu là bắt buộc"
        },
        ({ getFieldValue }) => ({
          validator(_, value) {
            if (!value || getFieldValue("newPassword") === value) {
              return Promise.resolve();
            }
            return Promise.reject(new Error("<PERSON>ật khẩu không khớp"));
          }
        })
      ],
      inputProps: {
        placeholder: "Xác nhận mật khẩu"
      }
    }
  ];
