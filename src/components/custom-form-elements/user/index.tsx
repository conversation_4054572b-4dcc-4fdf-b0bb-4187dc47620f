import AvatarUpload from "@/components/custom-form-item/avatar-upload";

export const userFormElementTypes = {
  avatar: AvatarUpload
} as unknown as App.Components.FormAdvance.DynamicRendererProps<App.Components.CustomFormElements.User.FormDataType>["elementTypes"];

export const userFormElements = (notRequired: string[] = []) => [
  {
    name: ["user", "avatar"],
    type: "avatar",
    className: "col-span-full"
  },
  {
    type: "title",
    label: "Thông tin Tài khoản",
    subTitle: "Thông tin tài khoản được sử dụng để đăng nhập vào hệ thống"
  },
  {
    name: ["user", "name"],
    label: "Tên hiển thị",
    type: "input",
    cols: 2,
    rules: [
      {
        required: !notRequired.includes("name"),
        message: "<PERSON>hông được để trống"
      }
    ],
    inputProps: {
      placeholder: "Nhập tên hiển thị"
    }
  },
  {
    name: ["user", "username"],
    label: "Tên đăng nhập",
    type: "input",
    cols: 2,
    rules: [
      {
        required: !notRequired.includes("username"),
        message: "Không được để trống"
      }
    ],
    inputProps: {
      placeholder: "Nhập tên đăng nhập"
    }
  },
  {
    name: ["user", "password"],
    label: "Mật khẩu",
    type: "randomPassword",
    cols: 2,
    rules: [
      {
        required: !notRequired.includes("password"),
        message: "Không được để trống"
      }
    ],
    inputProps: {
      placeholder: "Nhập mật khẩu"
    }
  },
  {
    name: ["user", "email"],
    label: "Email",
    type: "input",
    cols: 4,
    rules: [
      {
        required: !notRequired.includes("email"),
        message: "Không được để trống"
      }
    ],
    inputProps: {
      placeholder: "Nhập email"
    }
  },
  {
    name: ["user", "phone"],
    label: "Số điện thoại",
    type: "input",
    cols: 2,
    rules: [
      {
        required: !notRequired.includes("phone"),
        message: "Không được để trống"
      }
    ],
    inputProps: {
      placeholder: "Nhập số điện thoại"
    }
  }
];

export const editUserFormElements = (...notRequired: (string | undefined)[]) => [
  {
    name: ["user", "name"],
    label: "Tên hiển thị",
    type: "input",
    rules: [
      {
        required: !notRequired.includes("name"),
        message: "Không được để trống"
      }
    ],
    inputProps: {
      placeholder: "Nhập tên hiển thị"
    }
  },
  {
    name: ["user", "username"],
    label: "Tên đăng nhập",
    type: "input",
    rules: [
      {
        required: !notRequired.includes("username"),
        message: "Không được để trống"
      }
    ],
    inputProps: {
      placeholder: "Nhập tên đăng nhập"
    }
  },
  {
    name: ["user", "email"],
    label: "Email",
    type: "input",
    rules: [
      {
        required: !notRequired.includes("email"),
        message: "Không được để trống"
      }
    ],
    inputProps: {
      placeholder: "Nhập email"
    }
  },
  {
    name: ["user", "phone"],
    label: "Số điện thoại",
    type: "input",
    rules: [
      {
        required: !notRequired.includes("phone"),
        message: "Không được để trống"
      }
    ],
    inputProps: {
      placeholder: "Nhập số điện thoại"
    }
  }
];
