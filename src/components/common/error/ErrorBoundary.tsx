import React from 'react'
import { ErrorBoundary as ErrorBoundaryLib, type FallbackProps } from 'react-error-boundary'

export default function ErrorBoundary({ children }: React.PropsWithChildren) {
  const fallbackRender = React.useCallback(({ error }: FallbackProps) => {
    const e = error as Error
    return (
      <div role="alert">
        <p>Something went wrong:</p>
        <pre style={{ color: "red" }}>{e.message}</pre>
        <pre className='w-full break-words text-wrap p-4 text-small'>
          {e.stack?.split('\n')
            .map(line => line.split('@'))
            .map(([fn, path]) => (
              <div className='flex gap-2'>
                <strong>👉 {fn}</strong>
                <span className='text-gray-light'>{path}</span>
              </div>
            ))
          }
        </pre>
      </div>
    );
  }, [])

  return (
    <ErrorBoundaryLib fallbackRender={fallbackRender}>
      {children}
    </ErrorBoundaryLib>
  )
}
