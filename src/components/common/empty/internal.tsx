import { memo } from 'react';
import { Empty } from 'antd';

import { Typography, Button } from '@/atoms';
import { cn } from '@/utils';

const InternalEmpty = (props: App.Components.Empty.Props) => {
  const { description, children, className, onCreateNew } = props;

  return (
    <div className={cn('h-full w-full center bg-gray-50/80 rounded-2xl shadow-inner', className)}>
      <Empty
        className='center flex-col'
        image="https://gw.alipayobjects.com/zos/antfincdn/ZHrcdLPrvN/empty.svg"
        styles={{ image: { height: 80 } }}
        description={description || (
          <Typography.Text>
            Không có dữ liệu
          </Typography.Text>
        )}
      >
        {children || (<Button className="mb-4" type="primary" onClick={onCreateNew}>Tạo dữ liệu mới</Button>)}
      </Empty>
    </div>
  )
}

export default memo(InternalEmpty);
