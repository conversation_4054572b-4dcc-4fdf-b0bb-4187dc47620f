import React from 'react';
import { Watermark } from 'antd';
import { Icon } from '@iconify/react';

import { Typography } from '@/atoms';

const UnderConstruction = (props: { content?: React.ReactNode }) => {
  return (
    <Watermark
      className='h-full flex justify-center p-64'
      content={['MSPro', 'Mansa  Việt Nam']}
      height={50}
      width={150}
      // image="https://mdn.alipayobjects.com/huamei_7uahnr/afts/img/A*lkAoRbywo0oAAAAAAAAAAAAADrJ8AQ/original"
      
    >
      <div className='flex flex-col items-center gap-4'>
        <Icon icon='logos:bash-icon' className='text-[64px]'/>
        <Typography.Title level={1} type='danger'>Tính năng đang được phát triển</Typography.Title>
        <Typography.Text>{props.content}</Typography.Text>
      </div>
    </Watermark>
  );
};

export default React.memo(UnderConstruction);
