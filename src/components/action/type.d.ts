declare namespace App.Components.Action {
  export type ActionProps = {
    actionOptions: { key: string; value: string }[]
    options: App.Atoms.Select.SearchProps['options']
    selectClassName: string
    account: App.Components.Account
    actionData?: ActionData
  }

  export type ActionRef = {
    getValues: () => App.Components.ListItem
    clear: () => void
    setValues: (note: ListItem) => void
  }

  export type fieldsType = {
    name: string
    value: string | number | boolean | undefined | null | object | []
  }
}
