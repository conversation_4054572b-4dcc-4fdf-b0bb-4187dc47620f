import React from 'react'
import { DatePicker, Icon, Select } from '@/atoms'
import { Form } from 'antd'
import { ACTIONS, DATE_FORMAT } from './enum'
import dayjs from 'dayjs'
import './style.css'

const accounts: App.Components.Account[] = Array.from(
  { length: 5 },
  (_, index) => ({
    id: `${index + 1}`,
    username: `user${index + 1}`,
    name: `User ${index + 1}`,
    avatar: `https://randomuser.me/api/portraits/${
      index % 2 === 0 ? 'men' : 'women'
    }/${index + 1}.jpg`
  })
)

const Action = React.forwardRef<
  App.Components.Action.ActionRef,
  App.Components.Action.ActionProps
>(({ actionOptions, options, selectClassName, account, actionData }, ref) => {
  const [form] = Form.useForm()
  const [fields, setFields] =
    React.useState<App.Components.Action.fieldsType[]>()
  const dropdownStyle = React.useMemo(() => ({ width: 'fit-content' }), [])

  const getAccountsByIds = React.useCallback(
    (ids: Array<string | number>) =>
      ids?.reduce((accountList: App.Components.Account[], id) => {
        accounts.find((acc) => {
          if (acc.id == id) {
            accountList.push(acc)
          }
        })
        return accountList
      }, []),
    []
  )

  const renderActionField = React.useCallback(
    (
      actionKey: (typeof ACTIONS)[keyof typeof ACTIONS],
      placeholder: string
    ) => {
      const iconMap = {
        [ACTIONS.SCHEDULE]: 'mdi:calendar-blank-outline',
        [ACTIONS.ASSIGNED]: 'akar-icons:mention',
        [ACTIONS.RECORD]: 'material-symbols:arrow-outward-rounded'
      }

      return (
        <div className='relative action-container'>
          {iconMap[actionKey] && (
            <Icon
              icon={iconMap[actionKey]}
              size={14}
              className='absolute z-10 h-full flex items-center justify-center ml-2'
            />
          )}
          <Form.Item name={actionKey} className='m-0'>
            {actionKey === ACTIONS.SCHEDULE ? (
              <DatePicker
                format={DATE_FORMAT}
                defaultValue={dayjs()}
                placeholder='Today'
                variant='filled'
              />
            ) : (
              <Select.Search
                className={selectClassName}
                placeholder={placeholder}
                options={options}
                maxTagCount={1}
                mode='tags'
                dropdownStyle={dropdownStyle}
                optionLabelProp='label'
                variant='filled'
              />
            )}
          </Form.Item>
        </div>
      )
    },
    [selectClassName, options]
  )

  const handleSetFields = React.useCallback(
    (actionData: App.Components.ListItem) => {
      if (actionData) {
        const transformedData = Object.keys(actionData).reduce(
          (
            accumulator: App.Components.Action.fieldsType[],
            dataKey: string
          ): App.Components.Action.fieldsType[] => {
            if (
              Object.values(ACTIONS).includes(
                dataKey as (typeof ACTIONS)[keyof typeof ACTIONS]
              )
            ) {
              let value
              if (dataKey == ACTIONS.SCHEDULE) {
                value = dayjs(actionData[dataKey], DATE_FORMAT)
              } else {
                value = (
                  actionData[
                    dataKey as (typeof ACTIONS)[keyof typeof ACTIONS]
                  ] as Array<App.Components.Account>
                )?.map((account: App.Components.Account) => account.id)
              }
              accumulator.push({
                name: dataKey,
                value: value
              })
            }
            return accumulator
          },
          []
        )
        setFields(transformedData)
      }
    },
    []
  )

  React.useImperativeHandle(
    ref,
    () => ({
      getValues: () => {
        const formValues = form.getFieldsValue()
        return {
          [ACTIONS.ASSIGNED]: getAccountsByIds(formValues[ACTIONS.ASSIGNED]),
          [ACTIONS.RECORD]: getAccountsByIds(formValues[ACTIONS.RECORD]),
          [ACTIONS.SCHEDULE]: (formValues[ACTIONS.SCHEDULE] || dayjs()).format(
            DATE_FORMAT
          ),
          createdBy: account
        }
      },
      clear: form.resetFields,
      setValues: handleSetFields
    }),
    [form, account, getAccountsByIds, handleSetFields]
  )

  React.useEffect(() => {
    if (actionData) handleSetFields(actionData)
  }, [handleSetFields, actionData])

  return (
    <Form
      fields={fields}
      form={form}
      name='action-form'
      className='flex items-center gap-2'
    >
      {actionOptions.map(({ key, value }) =>
        renderActionField(
          key as (typeof ACTIONS)[keyof typeof ACTIONS],
          `Add ${value}`
        )
      )}
    </Form>
  )
})

Action.displayName = 'Action'

export default React.memo(Action)
