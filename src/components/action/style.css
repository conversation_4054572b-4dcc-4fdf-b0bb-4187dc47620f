.action-container .ant-popover-inner {
  padding: 0 !important;
}

.action-container .ant-select .ant-select-selector {
  padding: 1px 4px 1px 28px !important;
}

.action-container .ant-select.ant-select-open .ant-select-selection-item {
  color: inherit !important;
}

.ant-select-selection-item {
  background: transparent !important;
  border: none !important;
}

.action-container .ant-select .ant-select-arrow {
  right: 4px;
}

.ant-select-item-option-content div {
  gap: 15px !important;
}

.ant-select-selection-search-input {
  display: none;
}

.ant-select-selection-placeholder {
  padding-left: 15px;
}

.ant-picker {
  padding: 0px 4px 0px 28px !important;
  height: 32px !important;
  width: 130px !important;
}
