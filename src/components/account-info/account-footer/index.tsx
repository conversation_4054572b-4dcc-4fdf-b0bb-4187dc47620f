import { Button, Dropdown, Icon, Typography } from "@/atoms";
import React from "react";

const AccountFooterView = React.forwardRef<
  App.Components.AccountFooterRef,
  App.Components.AccountFooterProps
>((props, _) => {
  const { options, primaryButton, secondButton, onOptionSelect } = props;

  return (
    <div className="flex items-center justify-between">
      <div>
        <Button>
          {primaryButton.icon && <Icon icon={primaryButton.icon} />}
          <Typography.Text>{primaryButton.label}</Typography.Text>
        </Button>
      </div>
      <div>
        <Button>
          {secondButton.icon && <Icon icon={secondButton.icon} />}
          <Typography.Text>{secondButton.label}</Typography.Text>
        </Button>
      </div>
      <div>
        <Dropdown
          options={options}
          onOptionSelect={onOptionSelect}
          placement="topRight"
        >
          <div>
            <Icon className="cursor-pointer" icon="ri:more-fill" />
          </div>
        </Dropdown>
      </div>
    </div>
  );
});

AccountFooterView.displayName = "AccountFooterView";

export default AccountFooterView;
