import { Icon, Typography } from "@/atoms";
import { cn } from "@/utils";
import React, { useMemo } from "react";

const AccountInfo = React.forwardRef<
  App.Components.AccountInfoRef,
  App.Components.AccountInfoProps
>(({ title, accountInfo, footer, avatarProps, className }, _) => {
  const avatar = accountInfo?.avatar;

  const infoEntries = useMemo(() => {
    return Object.entries(accountInfo || {}).filter(
      ([key]) => key !== "avatar"
    );
  }, [accountInfo]);

  return (
    <div
      className={cn(
        "flex gap-5 border border-stroke p-5 rounded-xl shadow-xl",
        className
      )}
    >
      <div>
        {avatar && <img {...avatarProps} src={avatar} alt="User Avatar" />}
      </div>
      <div className="flex flex-col gap-5">
        <Typography.Title>{title}</Typography.Title>
        {infoEntries.map(([_, value], index) => (
          <div key={index} className="flex items-center gap-2">
            <Icon icon="material-symbols:info-outline" />
            <Typography.Text type="subtitle">{value}</Typography.Text>
          </div>
        ))}
        {footer}
      </div>
    </div>
  );
});

AccountInfo.displayName = "AccountInfo";

export default AccountInfo;
