declare namespace App.Components {
  export type AccountInfoProps = {
    className?: string;
    title?: string;
    accountInfo?: {
      [key: string]: string;
    };
    footer?: React.ReactNode;
    avatarProps?: {
      width: number;
      height: number;
    };
  };
  export type AccountInfoRef = {};

  export type AccountFooterProps = {
    primaryButton: {
      icon?: string;
      label?: string;
    };
    secondButton: {
      icon?: string;
      label?: string;
    };
    options: import("antd").MenuProps["items"];
    onOptionSelect?: (key: string) => void;
  };
  export type AccountFooterRef = {};
}
