import React from "react";
import { Divider, Input, Popover } from "antd";
import { <PERSON>ton, Typography } from "@/atoms";
import { Avatar } from "@/components";
import { Icon } from "@iconify/react";
import { cn } from "@/utils";
import { debounce } from "lodash";

const Assignees = React.forwardRef<
  App.Components.Assignees.AssigneesRef,
  App.Components.Assignees.AssigneesProps
>((_props, ref) => {
  const {
    data,
    trigger = "click",
    arrow = false,
    onChange,
    initialAssignees = [],
    triggerChange = ["onChange"],
    multiple = false,
    ...restProps
  } = _props;

  const [shallowData, setShallowData] =
    React.useState<App.Components.Assignees.Assignee[]>(data);
  const [assignees, setAssignees] =
    React.useState<App.Components.Assignees.Assignee[]>(initialAssignees);
  const [open, setOpen] = React.useState<boolean>(false);

  React.useImperativeHandle(ref, () => ({}), []);

  const handleSelectAssignee = React.useCallback(
    (assignee: App.Components.Assignees.Assignee) => {
      if (!multiple) {
        setAssignees([assignee]);

        if (onChange && triggerChange.includes("onChange")) {
          onChange({
            type: "onChange",
            assignees: [assignee]
          });
        }

        return;
      }

      const newAssignees = assignees.includes(assignee)
        ? assignees.filter((item) => item.id !== assignee.id)
        : [...assignees, assignee];

      setAssignees(newAssignees);

      if (onChange && triggerChange.includes("onChange")) {
        onChange({
          type: "onChange",
          assignees: newAssignees
        });
      }
    },
    [assignees, onChange, multiple, triggerChange]
  );

  const handleClearAssignees = React.useCallback(() => {
    setAssignees([]);

    if (onChange && triggerChange.includes("onClose")) {
      onChange({
        type: "onClose",
        assignees: []
      });
    }
  }, [onChange, triggerChange]);

  const handleSearch = React.useCallback(
    debounce((e: React.ChangeEvent<HTMLInputElement>) => {
      const { value } = e.target;

      if (value === "") {
        setShallowData(data);
        return;
      }

      const filteredData = data.filter((item) =>
        item.name.toLowerCase().includes(value.toLowerCase())
      );

      setShallowData(filteredData);
    }, 300),
    [shallowData]
  );

  const popoverContent = React.useMemo(() => {
    return (
      <div className="flex flex-col gap-2 min-w-[200px]">
        <div className="flex items-center justify-between">
          <Typography.Text className="font-bold text-center">
            Select assignees
          </Typography.Text>
          <Button
            size="small"
            type="text"
            className="px-1"
            onClick={handleClearAssignees}
          >
            <Typography.Text>Unassigned</Typography.Text>
          </Button>
        </div>
        <div className="flex flex-col gap-2">
          <Input
            placeholder="Enter to search"
            allowClear
            prefix={
              <Icon className="text-sm" icon="ant-design:search-outlined" />
            }
            onChange={handleSearch}
          />
          <Divider className="my-0" />
          <div className="flex flex-col gap-1 max-h-[200px] overflow-y-auto">
            {shallowData.length ? (
              shallowData.map((item) => {
                return (
                  <div
                    key={item.id}
                    className="flex items-center gap-2 hover:bg-primary/10 px-2 py-1 rounded-md cursor-pointer select-none"
                    onClick={() => handleSelectAssignee(item)}
                  >
                    <span
                      className={cn(
                        `text-sm`,
                        assignees.find((assignee) => assignee.id === item.id)
                          ? "text-primary"
                          : "text-transparent"
                      )}
                    >
                      <Icon icon="icon-park-outline:check-small" />
                    </span>
                    <div className="flex items-center gap-2">
                      <Avatar
                        avatar={item.avatar}
                        avatarDisplayName={item.name}
                        size={"small"}
                      />
                      <Typography.Text>{item.name}</Typography.Text>
                    </div>
                  </div>
                );
              })
            ) : (
              <div>No one bro</div>
            )}
          </div>
        </div>
      </div>
    );
  }, [assignees, data, handleSelectAssignee, shallowData]);

  const handleOpenChange = React.useCallback(
    (open: boolean) => {
      setOpen(open);

      if (onChange && triggerChange.includes("onClose") && !open) {
        onChange({
          type: "onClose",
          assignees: assignees
        });
      }
    },
    [assignees, onChange, triggerChange]
  );

  return (
    <Popover
      content={popoverContent}
      open={open}
      onOpenChange={handleOpenChange}
      trigger={trigger}
      arrow={arrow}
      {...restProps}
    />
  );
});

Assignees.displayName = "Assignees";

export default React.memo(Assignees);
