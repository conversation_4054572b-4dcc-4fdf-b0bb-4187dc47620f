declare namespace App.Components.Assignees {
  type PopoverProps = import("antd").PopoverProps;

  export interface Assignee extends App.Components.AvatarGroup.Avatar {}

  export type AssigneesChangeEvent = (info: {
    type: "onChange" | "onClose";
    assignees: Assignee[];
  }) => void;

  export interface AssigneesProps
    extends Omit<PopoverProps, "content" | "open" | "onOpenChange"> {
    data: Assignee[];
    initialAssignees?: Assignee[];
    onChange?: AssigneesChangeEvent;
    triggerChange?: Array<"onChange" | "onClose">;
    multiple?: boolean;
  }
  export type AssigneesRef = {};
}
