import React from 'react'
import { Text } from '@/atoms/typography/text'
import { Icon } from '@/atoms'
import ActivityItemDetail from './activity-detail'
import { cn } from '@/utils'

const ActivityItem = React.forwardRef<
  App.Components.ActivityItemRef,
  App.Components.ActivityItemProps
>(({ activity, handleToggleActivity: handleToggle }, ref) => {
  const [openActivities, setOpenActivities] = React.useState<Set<string>>(
    new Set()
  )

  const isActivityOpen = React.useCallback(
    (id: string) => openActivities.has(id),
    [openActivities]
  )

  const handleToggleActivity = React.useCallback((id: string) => {
    setOpenActivities((prevState) => {
      const newSet = new Set(prevState)
      newSet.has(id) ? newSet.delete(id) : newSet.add(id)
      return newSet
    })
  }, [])

  React.useImperativeHandle(ref, () => ({ handleToggleActivity }), [
    handleToggleActivity
  ])

  return (
    <div className='flex flex-col gap-5'>
      <div className='border-none'>
        <div
          className='cursor-pointer rounded-md'
          onClick={() => handleToggle?.(activity.id)}
        >
          <div className='flex flex-col gap-2'>
            <Text className='text-gray-light font-bold'>{activity.year}</Text>
            <div className='flex gap-2 items-center justify-between'>
              <div className='bg-stroke w-max text-nowrap rounded-md px-2'>
                <Text type='subtitle'>{activity.time}</Text>
              </div>
              <div className='bg-stroke w-full h-0.5'></div>
              <Icon
                icon={
                  isActivityOpen(activity.id)
                    ? 'lsicon:down-outline'
                    : 'lsicon:left-outline'
                }
                size={22}
              />
            </div>
          </div>
        </div>

        <div
          className={cn(
            'overflow-hidden transition-max-height duration-300 ease-in-out',
            !isActivityOpen(activity.id) ? 'max-h-[500px]' : 'max-h-0'
          )}
        >
          <div className='flex flex-col gap-4 py-4'>
            {activity.details.map((item) => (
              <ActivityItemDetail key={item.id} item={item} />
            ))}
          </div>
        </div>
      </div>
    </div>
  )
})

ActivityItem.displayName = 'ActivityItem'

export default React.memo(ActivityItem)
