import React from 'react'
import { Icon, ImageText } from '@/atoms'
import { Text } from '@/atoms/typography/text'
import { Flex } from 'antd'
import Typography from '@/atoms/typography'

const ContentBox: React.FC<{
  content: string
  avatar: string
  author: string
}> = ({ content, avatar, author }) => (
  <div className='hover:bg-secondary duration-300 flex flex-col gap-2 border border-stroke border-solid rounded-xl p-2'>
    <Text className='text-subtitle'>{content}</Text>
    <div className='bg-stroke rounded-lg w-max'>
      <ImageText src={avatar} alt={author ?? ''} text={author} />
    </div>
  </div>
)

const ActivityItemDetail = React.forwardRef<
  App.Components.ActivityItemDetailRef,
  App.Components.ActivityItemDetailProps
>(({ item }, _) => {
  const { action, author, avatar, createAt, content } = item

  return (
    <div className='flex gap-3 cursor-pointer'>
      <div className='flex flex-col items-center mt-1'>
        <div className='p-1 bg-light-hover rounded-md'>
          <Icon icon='iconamoon:file-thin' />
        </div>
        {content && <div className='line bg-stroke w-1/12 h-full' />}
      </div>

      <div className='flex flex-col gap-2 w-full'>
        <Flex align='center' justify='space-between'>
          <div className='flex items-center'>
            <ImageText
              textClassName='font-bold'
              src={avatar}
              alt={author ?? ''}
              text={author}
            />
            <Typography.Text className='font-bold text-gray-light'>
              {action}
            </Typography.Text>
          </div>
          <Text type='subtitle' className='font-bold text-gray-light'>
            {createAt}
          </Text>
        </Flex>

        {content && (
          <ContentBox content={content} avatar={avatar} author={author} />
        )}
      </div>
    </div>
  )
})

ActivityItemDetail.displayName = 'ActivityItemDetail'

export default React.memo(ActivityItemDetail)
