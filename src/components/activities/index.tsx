import React from 'react'

import { cn } from '@/utils'

import { useTableData, createColDefs } from '@/components';

import ActivityItem from './activity';

type Activity<TData extends object = {}> = App.DataTypes.Activity<TData>;

const Activities = <TData extends object>(
  props: App.Components.Activity.Props<TData>,
  ref: React.Ref<App.Components.Activity.Ref<TData>>
) => {
  const { className, api } = props;

  const colDefs = React.useMemo(() => {
    return createColDefs<Activity<TData>>([
      { cellRenderer: ActivityItem },
    ], { hideNumberOrderCol: true });
  }, []);

  const configs = React.useMemo<App.Components.TableData.Configs<Activity<TData>>>(() => ({
    api,
    tableId: 'activities',
    getRowId: (params) => params.data._id,
    colDefs,
    type: 'list',
    headerHeight: 0,
    hideTableTools: true,
    hideListBorder: true,
    rowHeight: 68,
    className: 'h-full',
    suppressHorizontalScroll: true,
  }), [api]);

  const { tableData } = useTableData<Activity<TData>>(configs);

  React.useImperativeHandle(ref, () => ({}), []);

  return (
    <div className={cn('h-full container mx-auto flex flex-col gap-2', className)}>
      {/* <Typography.Title level={1}>Hoạt động</Typography.Title> */}
      <div className='grow relative max-w-[1000px]'>
        {tableData}
      </div>
    </div>
  )
};

Activities.displayName = 'Activities'

export default React.memo(React.forwardRef(Activities)) as <
  TData extends object = {}
>(
  props: React.PropsWithoutRef<App.Components.Activity.Props<TData>> &
    React.RefAttributes<App.Components.Activity.Ref<TData>>
) => ReturnType<typeof Activities>;
