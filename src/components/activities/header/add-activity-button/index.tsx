import { Button, Icon } from '@/atoms'
import Typography from '@/atoms/typography'
import React from 'react'

const AddActivityButton = React.forwardRef<
  App.Components.AddActivityButtonRef,
  App.Components.AddActivityButtonProps
>(({ label }, _) => {
  return (
    <Button className='p-2 border-stroke flex items-center rounded-xl'>
      <Icon className='font-bold' icon='material-symbols:add' />
      <Typography.Title level={3} className='m-0'>
        {label}
      </Typography.Title>
    </Button>
  )
})

AddActivityButton.displayName = 'AddActivityButton'

export default React.memo(AddActivityButton)
