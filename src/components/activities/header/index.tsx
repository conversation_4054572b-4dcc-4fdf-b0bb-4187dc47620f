import React from 'react'
import AddActivityButton from './add-activity-button'
import Typography from '@/atoms/typography'

const ActivityHeader = React.forwardRef<
  App.Components.ActivityHeaderRef,
  App.Components.ActivityHeaderProps
>(({ title }, _) => (
  <div className='flex justify-between items-center'>
    <Typography.Title level={1} className='m-0'>
      {title}
    </Typography.Title>
    <AddActivityButton label='Add activity' />
  </div>
))

ActivityHeader.displayName = 'ActivityHeader'

export default React.memo(ActivityHeader)
