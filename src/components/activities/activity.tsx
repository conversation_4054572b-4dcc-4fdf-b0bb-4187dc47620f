import React from 'react';
import type { CustomCellRendererProps } from 'ag-grid-react';
import { Icon } from '@iconify/react';
import lodash from 'lodash';
import moment from 'moment';
import timeReadable from 'humanize-duration';

import { Typography, Popover, Tag } from '@/atoms';
import Avatar from '@/components/avatar';

import { ActivityActions, ActivityActionsMap, ActivityTargetTypesMap } from '@/constants';

type Activity = App.DataTypes.Activity<{}>;

const actionIcons = new Map([
  [ActivityActions.APPROVE, { icon: 'icon-park-outline:check-one', color: 'green' }],
  [ActivityActions.CHANGE, { icon: 'icon-park-outline:update-rotation', color: 'orange' }],
  [ActivityActions.CREATE, { icon: 'icon-park-outline:add-four', color: '#209bf3' }],
  [ActivityActions.DELETE, { icon: 'icon-park-outline:delete-five', color: 'red' }],
  [ActivityActions.REJECT, { icon: 'icon-park-outline:exclusive-gateway', color: 'magenta' }],
  [undefined, { icon: '', color: 'default' }]
])

function renderActionIcon (action?: Activity['action']) {
  let data = actionIcons.get(action);

  return (
    <div className='relative h-full flex flex-col items-center'>
      <div className='h-1 border-l'></div>
      <div className='w-[24px] h-[24px] bg-white rounded-full center border shadow'>
        <Icon icon={data?.icon || ''} color={data?.color}/>
      </div>
      <div className='h-[120px] border-l'></div>
    </div>
  )
}

function renderTime(time?: string) {
  if (!time) return ''

  return moment(time).format('hh:mm:ss DD/MM/YYYY')
}

function getDuration(time?: string) {
  if (!time) return <></>
  const t = moment(time)
  const milliseconds = moment().diff(t, 'milliseconds');
  return timeReadable(Math.abs(milliseconds), {
    language: 'vi',
    largest: 1
  })
}

const ActivityItem = (props: CustomCellRendererProps<Activity>) => {
  const { data } = props;

  if (!data) return <></>;

  return (
    <div className='grid grid-cols-[60px_minmax(500px,1fr)]' key={data._id}>
      {renderActionIcon(data?.action)}
      <div className='pb-4'>
        <div className="flex items-center gap-4">
          <div className='flex gap-1 items-center'>
            <Avatar avatar={data?.actor?.avatar} title={data?.actor?.name} size='small' />
            <Typography.Text type='secondary'>đã</Typography.Text>
            <Typography.Text type='secondary'>
              {ActivityActionsMap.get(data?.action as App.Constants.ActivityActions)?.title}
            </Typography.Text>

            {!!(data?.fields || []).find(f => f.fieldName === 'avatar') && (
              <Typography.Text type='secondary'>ảnh đại diện của </Typography.Text>
            )}
            
            <Typography.Text>
              {ActivityTargetTypesMap.get(data?.targetType as App.Constants.ActivityTargetTypes)?.title}
            </Typography.Text>
            <Typography.Title level={5} className='m-0'>{lodash.get(data, 'target.name')}</Typography.Title>
            
            {data?.action === ActivityActions.CHANGE && !(data?.fields || []).find(f => f.fieldName === 'avatar') && (
              <div>
                <Popover
                  content={(
                    <div className='flex flex-col gap-3'>
                      <Typography.Title level={3} className='ml-1 mb-0'>Thay đổi</Typography.Title>
                      {(data?.fields || []).map(field => {
                        if (field.fieldName === 'address') {
                          return (
                            <div
                              key={field.fieldName}
                              className='grid grid-cols-[14px_minmax(50px,1fr)_26px_minmax(50px,1fr)] gap-2 items-center'
                            >
                              <Typography.Text type='subtitle'>
                                <Icon icon='icon-park-outline:drag' />
                              </Typography.Text>
                              <Tag color='error' bordered className='w-[fit-content] border'>
                                <Typography.Text>Địa chỉ</Typography.Text>
                              </Tag>
                            </div>
                          )
                        }
                        return (
                          <div
                            key={field.fieldName}
                            className='grid grid-cols-[14px_minmax(50px,1fr)_26px_minmax(50px,1fr)] gap-2 items-center'
                          >
                            <Typography.Text type='subtitle'>
                              <Icon icon='icon-park-outline:drag' />
                            </Typography.Text>
                            <Tag color='error' bordered className='w-[fit-content] border'>
                              <Typography.Text>{lodash.get(field, 'oldValue') as string}</Typography.Text>
                            </Tag>
                            <div className='center'>
                              <Icon icon='icon-park-outline:double-right'/>
                            </div>
                            <Tag color='success' bordered className='w-[fit-content] border'>
                              <Typography.Text>{lodash.get(field, 'newValue') as string}</Typography.Text>
                            </Tag>
                          </div>
                        )
                      })}
                    </div>
                  )}
                >
                  <Icon icon='icon-park-outline:circle-double-right' className='text-[20px] ml-2 cursor-pointer' color='orange'/>
                </Popover>
              </div>
            )}
          </div>

          <div className='grow'>
            <div className='flex justify-between gap-2 px-4'>
              <div className='grow border-b border-dashed mb-1'></div>
              <Typography.Text type='secondary'>
                {getDuration(data.createdAt) + ' trước'}
              </Typography.Text>
            </div>
          </div>
        </div>

        <div className='ml-4'>
          <div className='flex items-center gap-1 ml-5'>
            <Typography.Text type='secondary' className='-translate-y-[1px]'>
              <Icon icon='solar:calendar-outline' />
            </Typography.Text>
            <Typography.Text type='secondary'>
              {renderTime(data?.createdAt || data?.deletedAt || data?.updatedAt)}
            </Typography.Text>
          </div>
        </div>
      </div>
    </div>
  )
}

export default React.memo(ActivityItem);
