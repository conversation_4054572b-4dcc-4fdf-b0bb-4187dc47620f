import React from 'react'
import CommentHeader from './comment-header'
import CommentList from './comment-list'
import AddComment from './add-comment'
export const CommentTab = React.forwardRef<
  App.Components.CommentTabRef,
  App.Components.CommentTabProps
>(({ account }, ref) => {
  const commentHeaderRef = React.useRef<App.Components.CommentHeaderRef>(null)
  const commentListRef = React.useRef<App.Components.CommentListRef>(null)
  const addCommentRef = React.useRef<App.Components.AddCommentRef>(null)

  React.useImperativeHandle(
    ref,
    () => ({
      getMentionMembers: () => addCommentRef.current!?.getMentionMembers(),
      getSelectedComment: () => commentListRef.current!.getSelectedComment(),
      getCommentList: () => commentListRef.current!.getCommentList(),

      resetComment: () => addCommentRef.current?.resetComment()
    }),
    []
  )

  return (
    <div className='flex flex-col gap-2'>
      <CommentHeader
        headerTitle={{ allComments: 'All comments', comments: 'Comments' }}
        ref={commentHeaderRef}
      />
      <CommentList account={account} ref={commentListRef} />
      <AddComment
        account={account}
        ref={addCommentRef}
        commentButtonLabel={{ comment: 'Comment', reply: 'Reply' }}
        textAreaPlaceholder={{
          reply: 'Reply...',
          addComment: 'Add comment...'
        }}
      />
    </div>
  )
})

CommentTab.displayName = 'CommentTab'

export default React.memo(CommentTab)
