type SelectCommentDetailData = App.Components.Comments.SelectCommentDetailData
type ResolveCommentData = App.Components.Comments.ResolveCommentData

export class SelectCommentEvent extends CustomEvent<SelectCommentDetailData> {
  static Name = 'select-comment-detail-event'
  constructor(data: SelectCommentDetailData) {
    super(SelectCommentEvent.Name, { detail: data })
  }
}

export class ResolveCommentEvent extends CustomEvent<ResolveCommentData> {
  static Name = 'resolve-comment-event'
  constructor(data: ResolveCommentData) {
    super(ResolveCommentEvent.Name, { detail: data })
  }
}
