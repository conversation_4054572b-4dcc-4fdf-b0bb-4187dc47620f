import React from 'react'
import { Button, Icon, Image, ImageText, Popover } from '@/atoms'
import Typography from '@/atoms/typography'
import { CommentActions } from '../enum'
import { isCommentResolvedBy } from '../helper'
import { useCommentInteraction } from '../hooks'

const commentOptions = [
  { icon: 'carbon:reply', key: CommentActions.SELECT_COMMENT },
  { icon: 'mdi:tick-circle-outline', key: CommentActions.RESOLVE_COMMENT }
]

const additionalCommentOptions = [
  {
    icon: 'si:copy-line',
    key: CommentActions.COPY_LINK_TO_COMMENT,
    label: 'Copy link to comment'
  },
  {
    icon: 'material-symbols:delete-outline-rounded',
    key: CommentActions.DELETE_COMMENT,
    label: 'Delete comment',
    danger: true
  }
]

const CommentItem = React.forwardRef<
  App.Components.CommentItemRef,
  App.Components.CommentItemProps
>(({ comment, account }, _) => {
  const {
    activeOptionId,
    handleMouseEnter,
    handleMouseLeave,
    handleOptionSelect
  } = useCommentInteraction({ comment, account: account! })

  const hasReplies = React.useMemo(
    () => (comment?.replices?.length || 0) > 0,
    [comment]
  )

  return (
    <div
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      className={`relative group  ${
        isCommentResolvedBy(comment, account) && 'opacity-50'
      }`}
    >
      <div
        className={`absolute w-max right-1 top-1 border border-stroke rounded-lg shadow-sm transition-opacity duration-300 z-1 ${
          activeOptionId === comment?.id ? 'opacity-100' : 'opacity-0'
        }`}
      >
        <div className='flex items-center opacity-100'>
          <CommentOptions
            comment={comment}
            account={account}
            onSelect={handleOptionSelect}
          />
          <CommentActionsPopover onSelect={handleOptionSelect} />
        </div>
      </div>

      <div>
        <div
          onClick={() => handleOptionSelect(CommentActions.SELECT_COMMENT)}
          className={`p-2 border border-solid rounded-lg border-secondary`}
        >
          <div>
            <div className='flex items-center gap-1'>
              <ImageText
                src={comment?.author.avatar}
                text={
                  <Typography.Text type='subtitle' className='font-bold'>
                    {comment?.author.name}
                  </Typography.Text>
                }
                preview={false}
              />
              <Typography.Text type='subtitle'>
                {comment?.commentAt}
              </Typography.Text>
            </div>
            <div
              className={`relative rounded-b-2xl px-2 pt-0 border-secondary ${
                hasReplies ? 'ml-4 pb-6 border-l-2 border-solid' : ''
              }`}
            >
              <div className='ml-3'>
                <Typography.Text className='text-wrap'>
                  {comment?.content}
                </Typography.Text>
              </div>
              {hasReplies && (
                <div className='absolute bottom-[-5px] left-6 flex items-center gap-3'>
                  <Image
                    className='object-contain'
                    width={14}
                    height={14}
                    src={comment?.author.avatar}
                  />
                  <Typography.Text>
                    {comment?.replices?.length} replies
                  </Typography.Text>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
})

CommentItem.displayName = 'CommentItem'

export default React.memo(CommentItem)

export const CommentOptions = ({
  onSelect,
  isReply = false,
  comment,
  account
}: {
  onSelect: (key: string) => void
  isReply?: boolean
  comment?: App.Components.CommentItem
  account?: App.Components.Account
}) => {
  return (
    <div className='pointer-events-auto'>
      {commentOptions.map(({ icon, key }) => {
        const newIcon =
          icon === 'mdi:tick-circle-outline' &&
          isCommentResolvedBy(comment, account)
            ? 'mdi:tick-circle'
            : icon

        return isReply && key === CommentActions.SELECT_COMMENT ? null : (
          <Button
            key={key}
            size='small'
            className='!p-0'
            type='text'
            shape='circle'
            onClick={() => onSelect(key)}
          >
            <Icon icon={newIcon} />
          </Button>
        )
      })}
    </div>
  )
}

export const CommentActionsPopover = ({
  onSelect
}: {
  onSelect: (key: string) => void
}) => (
  <Popover
    trigger={['click']}
    arrow={false}
    placement='bottomLeft'
    content={
      <div className='flex flex-col gap-2'>
        {[...additionalCommentOptions].map(({ icon, key, danger, label }) => (
          <div
            key={key}
            className='flex items-center gap-1 cursor-pointer'
            onClick={() => onSelect(key)}
          >
            <Icon icon={icon} color={danger ? 'red' : ''} />
            <Typography.Text
              className={`text-${danger ? 'red' : ''}`}
              type='subtitle'
            >
              {label}
            </Typography.Text>
          </div>
        ))}
      </div>
    }
  >
    <Button size='small' className='!p-0' type='text' shape='circle'>
      <Icon icon='iwwa:option' />
    </Button>
  </Popover>
)
