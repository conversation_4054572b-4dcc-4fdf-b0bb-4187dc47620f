import { Button, Icon } from '@/atoms'

interface CommentActionsProps {
  onEmojiClick: () => void
  onMentionClick: () => void
}

export const CommentActions: React.FC<CommentActionsProps> = ({
  onEmojiClick,
  onMentionClick
}) => (
  <div className='flex items-center gap-2'>
    <Button shape='circle' size='small' type='text' onClick={onEmojiClick}>
      <Icon icon='lucide:smile' />
    </Button>
    <Button shape='circle' size='small' type='text' onClick={onMentionClick}>
      <Icon icon='stash:at-symbol-light' />
    </Button>
  </div>
)
