import React from 'react'
import { Icon, Image, Button, Input, Dropdown } from '@/atoms'
import EmojiPicker from '@/components/emoij'
import { MenuProps } from 'antd'
import { uniqueId } from 'lodash'
import { TextAreaRef } from 'antd/es/input/TextArea'
import events, {
  AddCommentEvent,
  ReplyCommentEvent,
  SetIsCommentListVisibleEvent,
  SetMemberListEvent
} from '@/events'
import { useCommentInput, useEmojiPicker, useMentions } from '../hooks'

export const AddComment = React.forwardRef<
  App.Components.AddCommentRef,
  App.Components.AddCommentProps
>((props, ref) => {
  const { commentButtonLabel, textAreaPlaceholder, account } = props

  const {
    textareaRef,
    commentText,
    setCommentText,
    insertAtCursor,
    handleInputChange
  } = useCommentInput()

  const {
    isMentionVisible,
    mentionMembers,
    memberList,
    setMemberList,
    handleKeyDown,
    handleMentionSelect,
    setIsMentionVisible
  } = useMentions(insertAtCursor)

  const {
    isEmojiPickerVisible,
    toggleEmojiPicker,
    handleEmojiSelect,
    setIsEmojiPickerVisible
  } = useEmojiPicker(insertAtCursor)

  const [isCommentListVisible, setIsCommentListVisible] =
    React.useState<boolean>(true)

  const resetComment = React.useCallback(() => {
    setCommentText('')
    setIsEmojiPickerVisible(false)
    setIsMentionVisible(false)
  }, [])

  const onSetMemberListEvent: EventListener = React.useCallback((e) => {
    const { memberList } = (e as unknown as SetMemberListEvent).detail
    setMemberList(memberList)
  }, [])

  const onSetIsCommentListVisible: EventListener = React.useCallback((e) => {
    const { visible } = (e as unknown as SetIsCommentListVisibleEvent).detail
    setIsCommentListVisible(visible)
  }, [])

  const memberDropdownItems: MenuProps['items'] = React.useMemo(
    () =>
      memberList.map((member) => ({
        label: member.name,
        value: member.username,
        key: member.username
      })),
    [memberList]
  )

  const handleCommentSubmit = React.useCallback(() => {
    if (commentText.trim()) {
      const comment = {
        id: uniqueId('comment'),
        author: account!,
        commentAt: '30th Dec 2024',
        content: commentText,
        replices: [],
        resolvedBy: []
      }
      events.dispatchEvent(new AddCommentEvent({ comment }))
      resetComment()
    }
  }, [commentText, resetComment, account])

  const handleReplyCommentSubmit = React.useCallback(() => {
    if (commentText.trim()) {
      const reply = {
        id: uniqueId('comment'),
        author: account!,
        commentAt: '30th Dec 2024',
        content: commentText,
        replices: []
      }
      events.dispatchEvent(new ReplyCommentEvent({ reply }))
      resetComment()
    }
  }, [commentText, resetComment, account])

  const handleMentionTrigger = React.useCallback(() => {
    insertAtCursor('@')
    const textArea = textareaRef.current?.resizableTextArea?.textArea
    if (!textArea) return
    setIsMentionVisible(true)
  }, [insertAtCursor])

  const handleSubmit = React.useCallback(() => {
    if (isCommentListVisible) {
      handleCommentSubmit()
    } else {
      handleReplyCommentSubmit()
    }
  }, [isCommentListVisible, handleReplyCommentSubmit, handleCommentSubmit])

  React.useImperativeHandle(
    ref,
    () => ({
      resetComment,
      getMentionMembers: () => mentionMembers
    }),
    [mentionMembers, resetComment]
  )

  React.useEffect(() => {
    events.addEventListener(SetMemberListEvent.Name, onSetMemberListEvent)
    events.addEventListener(
      SetIsCommentListVisibleEvent.Name,
      onSetIsCommentListVisible
    )

    return () => {
      events.removeEventListener(SetMemberListEvent.Name, onSetMemberListEvent)
      events.removeEventListener(
        SetIsCommentListVisibleEvent.Name,
        onSetIsCommentListVisible
      )
    }
  }, [])

  return (
    <div>
      <div className='pl-3 pr-1 py-1 rounded-lg border border-secondary'>
        <div className='flex items-start'>
          <div className='flex items-center gap-2 mr-2 mt-3'>
            <Image
              className='object-contain'
              src={account?.avatar}
              alt={account?.name}
              height={14}
              width={14}
              preview={false}
            />
          </div>

          <div className='relative mt-2 w-full'>
            <Input.TextArea
              className='add-comment !border-none !focus:ring-0 p-0 !shadow-none !border-0 !outline-none'
              placeholder={
                isCommentListVisible
                  ? textAreaPlaceholder.addComment
                  : textAreaPlaceholder.reply
              }
              autoSize
              ref={textareaRef}
              value={commentText}
              rows={1}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
            />
            {isMentionVisible && (
              <Dropdown
                className='w-max'
                open={true}
                onOptionSelect={handleMentionSelect}
                options={memberDropdownItems}
              >
                <div></div>
              </Dropdown>
            )}
          </div>
        </div>

        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-2'>
            <div onClick={toggleEmojiPicker}>
              <Button shape='circle' size='small' type={'text'}>
                <Icon icon='lucide:smile' />
              </Button>
            </div>
            <div onClick={handleMentionTrigger}>
              <Button shape='circle' size='small' type={'text'}>
                <Icon icon='stash:at-symbol-light' />
              </Button>
            </div>
          </div>
          <div
            className={
              !commentText.trim() ? 'pointer-events-none opacity-50' : ''
            }
          >
            <Button
              className='rounded-lg py-3'
              type='primary'
              onClick={handleSubmit}
              size='small'
            >
              {isCommentListVisible
                ? commentButtonLabel.comment
                : commentButtonLabel.reply}
            </Button>
          </div>
        </div>
      </div>
      {isEmojiPickerVisible && (
        <div className='mt-2'>
          <EmojiPicker onEmojiClick={handleEmojiSelect} />
        </div>
      )}
    </div>
  )
})

AddComment.displayName = 'AddComment'

export default React.memo(AddComment)
