type AddCommentEventData = App.Components.Comments.AddCommentEventData
type ReplyCommentEventData = App.Components.Comments.ReplyCommentEventData
type SetMemberListEventData = App.Components.Comments.SetMemberListEventData
type SetMentionMemberListEventData =
  App.Components.Comments.SetMentionMemberListEventData
type SetIsReplyVisibleEventData =
  App.Components.Comments.SetIsReplyVisibleEventData

export class AddCommentEvent extends CustomEvent<AddCommentEventData> {
  static Name = 'add-comment-event'

  constructor(data: AddCommentEventData) {
    super(AddCommentEvent.Name, { detail: data })
  }
}

export class ReplyCommentEvent extends CustomEvent<ReplyCommentEventData> {
  static Name = 'reply-comment-event'

  constructor(data: ReplyCommentEventData) {
    super(ReplyCommentEvent.Name, { detail: data })
  }
}

export class SetMemberListEvent extends CustomEvent<SetMemberListEventData> {
  static Name = 'set-member-list-event'
  constructor(data: SetMemberListEventData) {
    super(SetMemberListEvent.Name, { detail: data })
  }
}

export class SetMentioMemberList extends CustomEvent<SetMentionMemberListEventData> {
  static Name = 'set-mention-member-list-event'
  constructor(data: SetMentionMemberListEventData) {
    super(SetMentioMemberList.Name, { detail: data })
  }
}

export class SetIsReplyVisibleEvent extends CustomEvent<SetIsReplyVisibleEventData> {
  static Name = 'set-is-reply-visible-event'
  constructor(data: SetIsReplyVisibleEventData) {
    super(SetIsReplyVisibleEvent.Name, { detail: data })
  }
}
