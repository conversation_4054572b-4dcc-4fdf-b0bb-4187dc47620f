declare namespace App.Components.Comments {
  export type AddCommentEventData = {
    comment: App.Components.CommentItem
  }

  export type ReplyCommentEventData = {
    reply: App.Components.CommentItem
  }

  export type SetMemberListEventData = {
    memberList: App.Components.Account[]
  }

  export type SetMentionMemberListEventData = {
    memberList: App.Components.Account[]
  }

  export type SetIsReplyVisibleEventData = {
    isReplyVisible: boolean
  }
}
