import React from 'react'
import CommentItem, {
  CommentActionsPopover,
  CommentOptions
} from '../comment-item'
import { Image } from '@/atoms'
import Typography from '@/atoms/typography'
import { Divider } from 'antd'
import { isCommentResolvedBy } from '../helper'
import { useCommentInteraction, useCommentList } from '../hooks'

interface CommentRepliesProps {
  replies: App.Components.CommentItem[]
}

export const CommentReplies: React.FC<CommentRepliesProps> = ({ replies }) => (
  <div className='mt-2'>
    {replies.map((reply) => (
      <div key={reply.id} className='flex gap-2 overflow-hidden mb-2'>
        <div className='flex flex-col'>
          <Image src={reply.author.avatar} width={18} preview={false} />
          <Divider type='vertical' className='h-full mt-1' />
        </div>
        <CommentDetail comment={reply} />
      </div>
    ))}
  </div>
)

export const CommentList = React.forwardRef<
  App.Components.CommentListRef,
  App.Components.CommentListProps
>(({ account }, ref) => {
  const { commentList, selectedComment } = useCommentList()
  const {
    activeOptionId,
    handleMouseEnter,
    handleMouseLeave,
    handleOptionSelect
  } = useCommentInteraction({ comment: selectedComment!, account })

  React.useImperativeHandle(
    ref,
    () => ({
      getCommentList: () => commentList,
      getSelectedComment: () => selectedComment
    }),
    [selectedComment, commentList]
  )

  /* Render selected comment detail with replies if exists, otherwise show comment list */
  return selectedComment ? (
    <div>
      <div className='flex flex-col gap-2'>
        <div
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          className={`flex justify-between relative group cursor-pointer ${
            isCommentResolvedBy(selectedComment, account) && 'opacity-50'
          }`}
        >
          <div className='flex items-start gap-2'>
            <div>
              <Image
                src={selectedComment.author.avatar}
                width={18}
                preview={false}
              />
            </div>
            <CommentDetail comment={selectedComment} />
          </div>
          <div
            className={`h-max border border-stroke rounded-lg shadow-sm transition-opacity duration-300 ${
              activeOptionId === selectedComment.id
                ? 'opacity-100'
                : 'opacity-0'
            }`}
          >
            <div className='flex items-center'>
              <CommentOptions
                account={account}
                comment={selectedComment}
                isReply={true}
                onSelect={handleOptionSelect}
              />
              <CommentActionsPopover onSelect={handleOptionSelect} />
            </div>
          </div>
        </div>

        {/* Show reply comment */}
        <div className='flex items-center'>
          {(selectedComment.replices?.length || 0) > 0 && (
            <div className='text-nowrap mr-2'>
              <Typography.Text type='subtitle'>
                {selectedComment.replices?.length} replices
              </Typography.Text>
            </div>
          )}
          <Divider className='m-0' />
        </div>
      </div>
      <div className='mt-2'>
        {(selectedComment?.replices || []).length > 0 && (
          <CommentReplies replies={selectedComment.replices || []} />
        )}
      </div>
    </div>
  ) : (
    <div className='flex flex-col gap-3 cursor-pointer'>
      {commentList.map((comment) => (
        <CommentItem account={account} comment={comment} key={comment.id} />
      ))}
    </div>
  )
})

CommentList.displayName = 'CommentList'

export default React.memo(CommentList)
const CommentDetail = ({
  comment
}: {
  comment: App.Components.CommentItem
}) => {
  return (
    <div>
      <div className='flex items-center gap-2'>
        <Typography.Text className='font-bold'>
          {comment.author.name}
        </Typography.Text>
        <Typography.Text type='subtitle'>{comment.commentAt}</Typography.Text>
      </div>
      <div>
        <Typography.Text>{comment.content}</Typography.Text>
      </div>
    </div>
  )
}
