type DeleteCommentData = App.Components.Comments.DeleteCommentEventData
type SetCommentListData = App.Components.Comments.SetCommentListEventData
type SetSelectedCommentEventData =
  App.Components.Comments.SetSelectedCommentEventData

export class DeleteCommentEvent extends CustomEvent<DeleteCommentData> {
  static Name = 'delete-comment-event'
  constructor(data: DeleteCommentData) {
    super(DeleteCommentEvent.Name, { detail: data })
  }
}

export class SetCommentListEvent extends CustomEvent<SetCommentListData> {
  static Name = 'set-comment-list-event'
  constructor(data: SetCommentListData) {
    super(SetCommentListEvent.Name, { detail: data })
  }
}

export class SetSelectedCommentEvent extends CustomEvent<SetSelectedCommentEventData> {
  static Name = 'set-selected-comment-event'
  constructor(data: SetSelectedCommentEventData) {
    super(SetSelectedCommentEvent.Name, { detail: data })
  }
}
