declare namespace App.Components {
  export type Account = {
    id: string
    username: string
    name: string
    avatar: string
  }

  export type CommentItem = {
    id?: string
    author: Account
    commentAt?: string
    content?: string
    replices?: CommentItem[]
    resolvedBy?: Account[]
  }

  export type EmoijityItem = { emoji: string; imageUrl: string }

  export type CommentHeaderProps = {
    headerTitle: {
      [key: string]: string
    }
  }
  export type CommentHeaderRef = {}

  export type CommentTabProps = {
    account?: App.Components.Account
  }
  export type CommentTabRef = {
    getSelectedComment: () => CommentItem | null
    getCommentList: () => CommentItem[]
    getMentionMembers: () => Account[]
    resetComment: () => void
  }

  export type CommentListProps = {
    account: App.Components.Account
  }
  export type CommentListRef = {
    getSelectedComment: () => CommentItem | null
    getCommentList: () => CommentItem[]
  }

  export type CommentItemProps = {
    comment: CommentItem
    account?: App.Components.Account
  }
  export type CommentItemRef = {}

  export type AddCommentRef = {
    resetComment: () => void
    getMentionMembers: () => Account[]
  }
  export type AddCommentProps = {
    commentButtonLabel: {
      [key: string]: string
    }
    textAreaPlaceholder: {
      [key: string]: string
    }
    account?: App.Components.Account
  }
}
