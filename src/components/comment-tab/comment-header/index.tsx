import React from 'react'
import { Icon, Dropdown, Button } from '@/atoms'
import Typography from '@/atoms/typography'
import events, {
  BackToCommentListEvent,
  SetIsCommentListVisibleEvent
} from '@/events'

const COMMENT_OPTIONS = [
  {
    label: 'Show resolved comments',
    key: 'showResolved',
    icon: <Icon size={16} icon='material-symbols-light:star-outline' />
  },
  {
    label: 'Only my threads',
    key: 'myThreads',
    icon: <Icon size={16} icon='bx:detail' />
  }
]

export const CommentHeader = React.forwardRef<
  App.Components.CommentHeaderRef,
  App.Components.CommentHeaderProps
>((props, ref) => {
  const { headerTitle } = props

  const [isCommentListVisible, setIsCommentListVisible] =
    React.useState<boolean>(true)

  const handleOptionSelect = (optionKey: string) => {}

  const handleBackToCommentList = React.useCallback(() => {
    events.dispatchEvent(new BackToCommentListEvent({}))
  }, [])

  const onSetIsCommentListVisible: EventListener = React.useCallback((e) => {
    const { visible } = (e as unknown as SetIsCommentListVisibleEvent).detail
    setIsCommentListVisible(visible)
  }, [])

  React.useImperativeHandle(ref, () => ({}), [])

  React.useEffect(() => {
    events.addEventListener(
      SetIsCommentListVisibleEvent.Name,
      onSetIsCommentListVisible
    )

    return () => {
      events.removeEventListener(
        SetIsCommentListVisibleEvent.Name,
        onSetIsCommentListVisible
      )
    }
  }, [])

  return (
    <div>
      <div>
        {isCommentListVisible ? (
          <div className='flex items-center justify-between w-full'>
            <Typography.Text className='font-bold'>
              {headerTitle?.allComments}
            </Typography.Text>
            <CommentOptionsDropdown onSelect={handleOptionSelect} />
          </div>
        ) : (
          <div className='flex items-center'>
            <Button
              shape='circle'
              type={'text'}
              size='small'
              className='p-0 mr-2'
              onClick={handleBackToCommentList}
            >
              <Icon icon='weui:back-outlined' />
            </Button>
            <Typography.Text className='font-bold'>
              {headerTitle.comments}
            </Typography.Text>
          </div>
        )}
      </div>
    </div>
  )
})

CommentHeader.displayName = 'CommentHeader'

export default React.memo(CommentHeader)

const CommentOptionsDropdown = ({
  onSelect
}: {
  onSelect: (key: string) => void
}) => {
  return (
    <div className='rounded-md'>
      <Dropdown
        className='border-0 outline-none'
        options={COMMENT_OPTIONS}
        placement='bottomLeft'
        trigger={['click']}
        onOptionSelect={onSelect}
      >
        <div className='cursor-pointer'>
          <Icon icon='mdi:mixer-settings' />
        </div>
      </Dropdown>
    </div>
  )
}
