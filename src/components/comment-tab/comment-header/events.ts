type BackToCommentListData = App.Components.Comments.BackToCommentListData
type SetIsCommentListVisible = App.Components.Comments.SetIsCommentListVisible

export class BackToCommentListEvent extends CustomEvent<BackToCommentListData> {
  static Name = 'back-to-comment-list-event'
  constructor(data: BackToCommentListData) {
    super(BackToCommentListEvent.Name, { detail: data })
  }
}

export class SetIsCommentListVisibleEvent extends CustomEvent<SetIsCommentListVisible> {
  static Name = 'set-is-comment-list-visible-event'
  constructor(data: SetIsCommentListVisible) {
    super(SetIsCommentListVisibleEvent.Name, { detail: data })
  }
}
