import React from 'react'

export const useEmojiPicker = (insertAtCursor: (text: string) => void) => {
  const [isEmojiPickerVisible, setIsEmojiPickerVisible] = React.useState(false)

  const toggleEmojiPicker = React.useCallback(() => {
    setIsEmojiPickerVisible((prev) => !prev)
  }, [])

  const handleEmojiSelect = React.useCallback(
    ({ emoji }: App.Components.EmoijityItem) => {
      insertAtCursor(emoji)
    },
    [insertAtCursor]
  )

  return {
    isEmojiPickerVisible,
    toggleEmojiPicker,
    handleEmojiSelect,
    setIsEmojiPickerVisible
  }
}
