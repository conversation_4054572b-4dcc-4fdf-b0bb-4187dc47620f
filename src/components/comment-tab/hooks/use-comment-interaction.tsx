import React from 'react'
import { CommentActions } from '../enum'
import events, {
  DeleteCommentEvent,
  ResolveCommentEvent,
  SelectCommentEvent
} from '@/events'

interface UseCommentInteractionProps {
  comment: App.Components.CommentItem
  account: App.Components.Account
  onSelect?: (key: string) => void
}

/**
 * A custom hook for handling comment interaction behaviors
 * - activeOptionId: Current active option ID for hover states
 * - handleMouseEnter: Function to handle mouse enter events
 * - handleMouseLeave: Function to handle mouse leave events
 * - handleOptionSelect: Function to handle option selection events
 */
export const useCommentInteraction = ({
  comment,
  account,
  onSelect
}: UseCommentInteractionProps) => {
  const [activeOptionId, setActiveOptionId] = React.useState<string>()

  const handleMouseEnter = React.useCallback(() => {
    if (comment?.id) {
      setActiveOptionId(comment.id)
    }
  }, [comment])

  const handleMouseLeave = React.useCallback(() => {
    setActiveOptionId(undefined)
  }, [])

  const handleOptionSelect = React.useCallback(
    (key: string) => {
      if (!account) return

      switch (key) {
        case CommentActions.SELECT_COMMENT:
          events.dispatchEvent(
            new SelectCommentEvent({ selectedComment: comment })
          )
          break
        case CommentActions.DELETE_COMMENT:
          events.dispatchEvent(new DeleteCommentEvent({ comment }))
          break
        case CommentActions.RESOLVE_COMMENT:
          events.dispatchEvent(
            new ResolveCommentEvent({
              comment,
              account
            })
          )
          break
        default:
          onSelect?.(key)
      }
    },
    [comment, account, onSelect]
  )

  return {
    activeOptionId,
    handleMouseEnter,
    handleMouseLeave,
    handleOptionSelect
  }
}
