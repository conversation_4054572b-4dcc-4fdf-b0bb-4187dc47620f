import { TextAreaRef } from "antd/lib/input/TextArea"
import React from "react"

export const useCommentInput = () => {
  const textareaRef = React.useRef<TextAreaRef>(null)
  const [commentText, setCommentText] = React.useState<string>('')

  const insertAtCursor = React.useCallback((text: string) => {
    const textArea = textareaRef.current?.resizableTextArea?.textArea
    if (!textArea) return

    const { selectionStart = 0, selectionEnd = 0 } = textArea
    setCommentText(
      (prev) => prev.slice(0, selectionStart) + text + prev.slice(selectionEnd)
    )

    setTimeout(() => {
      textArea.setSelectionRange(
        selectionStart + text.length,
        selectionStart + text.length
      )
      textArea.focus()
    }, 0)
  }, [])

  const handleInputChange = React.useCallback(
    (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      setCommentText(e.target.value)
    },
    []
  )

  return {
    textareaRef,
    commentText,
    setCommentText,
    insertAtCursor,
    handleInputChange
  }
}