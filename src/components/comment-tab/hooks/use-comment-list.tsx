import React from 'react'
import { SetCommentListEvent, SetSelectedCommentEvent } from '../events'
import events from '@/events'

/**
 * A custom hook for managing comment list state and event listeners
 * This hook handles:
 * - Comment list state management
 * - Selected comment state management
 * - Event listeners for comment list updates
 * - Event listeners for selected comment updates
 */
export const useCommentList = () => {
  const [commentList, setCommentList] = React.useState<
    App.Components.CommentItem[]
  >([])
  const [selectedComment, setSelectedComment] =
    React.useState<App.Components.CommentItem | null>(null)

  const onSetCommentList = React.useCallback((e: Event) => {
    const { commentList } = (e as SetCommentListEvent).detail
    setCommentList(commentList)
  }, [])

  const onSetSelectedComment = React.useCallback((e: Event) => {
    const { selectedComment } = (e as SetSelectedCommentEvent).detail
    setSelectedComment(selectedComment)
  }, [])

  React.useEffect(() => {
    events.addEventListener(SetCommentListEvent.Name, onSetCommentList)
    events.addEventListener(SetSelectedCommentEvent.Name, onSetSelectedComment)

    return () => {
      events.removeEventListener(SetCommentListEvent.Name, onSetCommentList)
      events.removeEventListener(
        SetSelectedCommentEvent.Name,
        onSetSelectedComment
      )
    }
  }, [onSetCommentList, onSetSelectedComment])

  return {
    commentList,
    selectedComment,
    setCommentList,
    setSelectedComment
  }
}
