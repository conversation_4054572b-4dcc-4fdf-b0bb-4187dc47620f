import React from 'react'

export const useMentions = (insertAtCursor: (text: string) => void) => {
  const [isMentionVisible, setIsMentionVisible] = React.useState(false)
  const [mentionMembers, setMentionMembers] = React.useState<
    App.Components.Account[]
  >([])
  const [memberList, setMemberList] = React.useState<App.Components.Account[]>(
    []
  )

  const handleKeyDown = React.useCallback(
    (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
      if (e.key === '@') {
        const textArea = e.currentTarget
        const cursorPos = textArea.selectionStart
        if (cursorPos === 0 || textArea.value[cursorPos - 1] === ' ') {
          setIsMentionVisible(true)
        }
      } else {
        setIsMentionVisible(false)
      }
    },
    []
  )

  const handleMentionSelect = React.useCallback(
    (username: string) => {
      insertAtCursor(username)
      setIsMentionVisible(false)

      const member = memberList.find((m) => m.username === username)
      setMentionMembers((prev) => [...prev, member!])
    },
    [insertAtCursor, memberList]
  )

  return {
    isMentionVisible,
    mentionMembers,
    memberList,
    setIsMentionVisible,
    setMemberList,
    handleKeyDown,
    handleMentionSelect
  }
}
