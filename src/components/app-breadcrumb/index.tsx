import React from 'react';
import { Breadcrumb as AntdBreadcrumb } from 'antd';
import type { ItemType } from 'antd/es/breadcrumb/Breadcrumb';
import { Link, useNavigate, useLocation, matchPath, createPath } from 'react-router';
import { Icon } from '@iconify/react';

import { TabsMap } from '@/constants';
import { Button, Typography } from '@/atoms';
import events, { UpdateBreadcrumbItemName } from '@/events';

type Props = App.Components.Breadcrumb.Props;

const configs = new Map([
  ['branches', { title: 'DS Chi nhánh', href: '/branches' }],
  ['departments', { title: 'DS Phòng ban', href: '/departments' }],
  ['staffs', { title: 'DS Nhân viên', href: '/staffs' }],
  ['customers', { title: 'DS Khách hàng', href: '/customers' }],
  ['workers', { title: '<PERSON> <PERSON><PERSON><PERSON><PERSON> lao động', href: '/workers' }],
  ['vendors', { title: '<PERSON> <PERSON><PERSON> tá<PERSON>', href: '/vendors' }],
  ['collaborators', { title: 'DS Cộng tác viên', href: '/collaborators' }],
  ['organizations', { title: 'DS Tổ chức', href: '/organizations' }],
]);

const Breadcrumb = (props: Props) => {
  const [category, setCategory] = React.useState<ItemType>();
  const [item, setItem] = React.useState<ItemType>();
  const [feature, setFeature] = React.useState<ItemType>();
  const location = useLocation();

  const navigate = useNavigate();

  const itemsRender = React.useCallback<NonNullable<Props['itemRender']>>((currentRoute, _params, items, paths) => {
    const isLast = currentRoute?.path === items[items.length - 1]?.path;
    return isLast ? (
      <span>{currentRoute.title}</span>
    ) : (
      <Link to={`/${paths.join("/")}`}>{currentRoute.title}</Link>
    );
  }, []);

  const goBack = React.useCallback(() => {
    navigate(-1);
  }, [navigate]);

  const items = React.useMemo<NonNullable<Props['items']>>(() => {
    return [category, item, feature].filter(Boolean) as NonNullable<Props['items']>;
  }, [category, item, feature]);

  const updateBreadcrumbByLocationPath = React.useCallback(() => {
    const { pathname } = location;

    const match = matchPath('/:category/:item?/:feature?', pathname);
    let { category, item, feature } = match?.params || {};
    

    const categoryConfig = !category ? configs.get('branches') : configs.get(category as string);
    setCategory({
      title: (
        <div className='center gap-1 h-full'>
          <Icon icon='icon-park-outline:all-application' className='text-blue-600'/>
          <Typography.Text className='mt-[2px]'>{categoryConfig?.title}</Typography.Text>
        </div>
      ),
      path: categoryConfig?.href
    });

    if (item) {
      setItem(old => {
        if (old) return old;

        return {
          title: (
            <div className='center gap-1 h-full'>
              <Icon icon='icon-park-outline:check-one' className='text-green-600'/>
              <Typography.Text className='mt-[2px]'>{'Chi tiết: ' + item}</Typography.Text>
            </div>
          ),
          path: createPath({ pathname: `/${category}/${item}` })
        }
      });
    } else {
      setItem(undefined);
    }

    if (feature) {
      const config = TabsMap.get(feature);
      setFeature({
        title: (
          <div className='center gap-1 h-full'>
            <Icon icon='icon-park-outline:other' className='text-orange-600'/>
            <Typography.Text className='mt-[2px]'>{config?.title}</Typography.Text>
          </div>
        )
      });
    } else {
      setFeature(undefined);
    }

  }, [location]);

  React.useEffect(() => {
    updateBreadcrumbByLocationPath();
  }, [updateBreadcrumbByLocationPath]);

  React.useEffect(() => {
    const listener: EventListener = (e) => {
      const event = e as UpdateBreadcrumbItemName;
      if (event.detail.title) {
        setItem(old => ({
          ...old,
          title: (
            <div className='center gap-1 h-full'>
              <Icon icon='icon-park-outline:check-one' className='text-green-600'/>
              <Typography.Text className='mt-[2px]'>{'Chi tiết: ' + event.detail.title}</Typography.Text>
            </div>
          )
        }));
      }
    };

    events.addEventListener(UpdateBreadcrumbItemName.Name, listener);

    return () => events.removeEventListener(UpdateBreadcrumbItemName.Name, listener);
  }, []);

  return (
    <div className='flex gap-2 items-center m-0'>
      <Button onClick={goBack} size='small' type='text' shape='circle'>
        <Icon icon='icon-park-outline:left-small' />
      </Button>
      <AntdBreadcrumb
        {...props}
        items={items}
        itemRender={itemsRender}
        className='-mb-[2px]'
        separator={<Icon icon='icon-park-outline:right' className='h-full'/>}
      />
    </div>
  );
};

Breadcrumb.displayName = 'Breadcrumb';

export default React.memo(Breadcrumb);
