import { useQuery } from "@tanstack/react-query";
import { Form, Select } from "antd";
import React from "react";
import { StaffService, BranchService } from "@/services";

const staffService = new StaffService();
const branchService = new BranchService();

const AssignedStaffSelect = (
  props: App.Components.CustomFormItem.AssignedStaffSelect.Props
) => {
  const {
    form,
    name = ["branch", "assignedStaff"],
    label = ["Chi nhánh", "Nhân viên được phân công"],
    placeholder = ["Chọn chi nhánh", "Chọn chi nhánh trước"],
    isRequired = true
  } = props;

  const [branch, setBranch] = React.useState<string | undefined>(undefined);

  const { data: branchOptions } = useQuery({
    queryKey: ["branches"],
    queryFn: () =>
      branchService.getBranches().then((res) => res.data).then((data) => {
        return data?.results?.map((item) => ({
          value: item._id,
          label: item.name
        }));
      }),
  });

  const { data: staffOptions } = useQuery({
    queryKey: ["staffs", branch],
    queryFn: () =>
      staffService
        .getStaffs({ branch: branch })
        .then((res) => res.data)
        .then((data) => {
          return data?.results?.map((item) => ({
            value: item._id,
            label: item.user.name
          }));
        }),
    enabled: !!branch
  });

  const branchWatch = Form.useWatch(name[0], form);

  React.useEffect(() => {
    setBranch(branchWatch);
  }, [branchWatch]);

  const handleSelect = React.useCallback((value: string) => {
    setBranch(value);
    form.setFieldValue(name[1], undefined);
  }, [form, name]);

  return (
    <div className="grid grid-cols-2 gap-4">
      <Form.Item className="mb-0" name={name[0]} label={label[0]} rules={[{ required: isRequired, message: "Vui lòng chọn chi nhánh" }]}>
        <Select options={branchOptions} placeholder={placeholder[0]} onSelect={handleSelect} />
      </Form.Item>
      <Form.Item className="mb-0" name={name[1]} label={label[1]} rules={[{ required: isRequired, message: "Vui lòng chọn nhân viên được phân công" }]}>
        <Select options={staffOptions} placeholder={placeholder[1]} />
      </Form.Item>
    </div>
  );
};

export default AssignedStaffSelect;
