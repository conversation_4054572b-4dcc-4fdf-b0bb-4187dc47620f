import React from "react";
import { Select, SelectProps } from "antd";
import { GenderMap } from "@/constants";
import { convertMapToArray } from "@/constants/utils";

const GenderOptions = convertMapToArray(GenderMap);

type GenderSelectProps = Omit<SelectProps, "options">;

const GenderSelect = ({ ...restProps }: GenderSelectProps) => {
  return <Select options={GenderOptions} {...restProps} />;
};

export default React.memo(GenderSelect) as React.ComponentType<GenderSelectProps>;
