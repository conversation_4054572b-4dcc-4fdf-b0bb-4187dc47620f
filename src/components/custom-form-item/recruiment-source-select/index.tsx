import { RecruitmentSourceTypes, RecruitmentSourceTypesMap } from "@/constants";
import { convertMapToArray } from "@/constants/utils";
import { Select, Form } from "antd";
import { useQuery } from "@tanstack/react-query";
import {
  StaffService,
  VendorService,
  OrganizationService,
  CollaboratorService
} from "@/services";
import React from "react";

const staffService = new StaffService();
const vendorService = new VendorService();
const organizationService = new OrganizationService();
const collaboratorService = new CollaboratorService();

const recruitmentSourceOptions = convertMapToArray(RecruitmentSourceTypesMap);

const RecruitmentSourceSelect = (
  props: App.Components.CustomFormItem.RecruitmentSourceSelect.Props
) => {
  const {
    form,
    name = ["recruitmentSource", "recruitmentSourceType"],
    label = ["Chọn nguồn tuyển dụng", "Chọn loại nguồn tuyển dụng"],
    placeholder = ["Chọn nguồn tuyển dụng", "Chọn loại nguồn tuyển dụng"]
  } = props;
  const [recruitmentSourceType, setRecruitmentSourceType] = React.useState<
    (typeof RecruitmentSourceTypes)[keyof typeof RecruitmentSourceTypes]
  >(RecruitmentSourceTypes.STAFF);

  const recruitmentSourceTypeWatch = Form.useWatch(name[1], form);

  React.useEffect(() => {
    setRecruitmentSourceType(recruitmentSourceTypeWatch);
  }, [recruitmentSourceTypeWatch]);

  const { data: staffs } = useQuery({
    queryKey: ["staffs", recruitmentSourceType],
    queryFn: () =>
      staffService
        .getStaffs()
        .then((res) => res.data)
        .then((data) =>
          data?.results?.map((item) => ({
            value: item._id,
            label: item.user.name
          }))
        ),
    enabled: recruitmentSourceType === RecruitmentSourceTypes.STAFF
  });

  const { data: vendors } = useQuery({
    queryKey: ["vendors", recruitmentSourceType],
    queryFn: () =>
      vendorService
        .getVendors()
        .then((res) => res.data)
        .then((data) =>
          data?.results?.map((item) => ({
            value: item._id,
            label: item.user.name
          }))
        ),
    enabled: recruitmentSourceType === RecruitmentSourceTypes.VENDOR
  });

  const { data: organizations } = useQuery({
    queryKey: ["organizations", recruitmentSourceType],
    queryFn: () =>
      organizationService
        .getOrganizations()
        .then((res) => res.data)
        .then((data) =>
          data?.results?.map((item) => ({
            value: item._id,
            label: item.name
          }))
        ),
    enabled: recruitmentSourceType === RecruitmentSourceTypes.ORGANIZATION
  });

  const { data: collaborators } = useQuery({
    queryKey: ["collaborators", recruitmentSourceType],
    queryFn: () =>
      collaboratorService
        .getCollaborators()
        .then((res) => res.data)
        .then((data) =>
          data?.results?.map((item) => ({
            value: item._id,
            label: item.name
          }))
        ),
    enabled: recruitmentSourceType === RecruitmentSourceTypes.COLLABORATOR
  });

  const options = React.useMemo(
    () => ({
      [RecruitmentSourceTypes.STAFF]: staffs,
      [RecruitmentSourceTypes.VENDOR]: vendors,
      [RecruitmentSourceTypes.ORGANIZATION]: organizations,
      [RecruitmentSourceTypes.COLLABORATOR]: collaborators
    }),
    [staffs, vendors, organizations, collaborators]
  );

  const handleSearch = (value: string) => {
    return options[recruitmentSourceType]?.filter((item) =>
      item.label.toLowerCase().includes(value.toLowerCase())
    );
  };

  const handleSelect = React.useCallback  (
    (value: string) => {
      setRecruitmentSourceType(value as (typeof RecruitmentSourceTypes)[keyof typeof RecruitmentSourceTypes]);
      form.setFieldValue(name[0], undefined);
    },
    [form, name]
  );

  return (
    <div className="col-span-full grid grid-cols-2 gap-4">
      <Form.Item
        initialValue={recruitmentSourceOptions[0].value}
        name={name[1]}
        label={label[1]}
      >
        <Select
          options={recruitmentSourceOptions}
          placeholder={placeholder[1]}
          onSelect={handleSelect}
        />
      </Form.Item>
      <Form.Item name={name[0]} label={label[0]}>
        <Select
          options={options[recruitmentSourceType] || []}
          placeholder={placeholder[0]}
          onSearch={handleSearch}
          showSearch
        />
      </Form.Item>
    </div>
  );
};

export default RecruitmentSourceSelect;
