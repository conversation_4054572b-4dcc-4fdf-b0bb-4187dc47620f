import React from "react";
import { useQuery } from "@tanstack/react-query";
import { Select, SelectProps } from "antd";
import { CustomerService } from "@/services";

const service = new CustomerService();

const CustomerSelect = (props: SelectProps) => {
  const { data: customers } = useQuery({
    queryKey: ["customers"],
    queryFn: () =>
      service
        .getCustomers()
        .then((res) => res.data)
        .then((data) =>
          data?.results.map((customer) => ({
            label: customer.name,
            value: customer._id
          }))
        )
  });

  return <Select options={customers || []} {...props} />;
};

export default React.memo(CustomerSelect);
