import React from "react";
import { Avatar, theme, Upload, UploadProps } from "antd";
import { ACCEPTED_IMAGE_TYPES } from "@/contants/commons";
import { Button, Typography } from "@/atoms";
import ImgCrop from "antd-img-crop";
import { UploadService } from "@/services";
import { useMutation } from "@tanstack/react-query";
import { Icon } from "@iconify/react/dist/iconify.js";

const uploadService = new UploadService();

const AvatarUpload = (
  props: App.Components.CustomFormItem.AvatarUpload.Props
) => {
  const { value, onChange } = props;
  const { token } = theme.useToken();

  const [previewUrl, setPreviewUrl] = React.useState<string | undefined>();

  const customAvatarStyle = React.useMemo<React.CSSProperties>(
    () => ({
      ...(!value
        ? { backgroundColor: token.colorPrimary }
        : { borderColor: token.colorBorder })
    }),
    [token, value]
  );

  const uploadMutation = useMutation({
    mutationFn: (file: File) => {
      return uploadService.uploadAvatar(file).then((res) => res.data);
    }
  });

  const customRequest: UploadProps["customRequest"] = (options) => {
    const { onSuccess, onError, file } = options;

    uploadMutation.mutate(file as File, {
      onSuccess: (res) => {
        setPreviewUrl(res?.url);
        onChange?.(res?.url);
        onSuccess?.(res);
      },
      onError: (error) => {
        onChange?.(undefined);
        onError?.(error);
      }
    });
  };

  return (
    <div className="flex gap-4 items-end">
      <Avatar
        size={64}
        className="rounded-lg bg-center"
        style={customAvatarStyle}
        src={value || previewUrl}
      >
        A
      </Avatar>
      <div className="flex flex-col">
        <Typography.Text type="subtitle" className="mb-0">
          Chỉ hỗ trợ tải lên ảnh kiểu PNG, JPEG và dung lượng dưới 20MB
        </Typography.Text>
        <ImgCrop rotationSlider>
          <Upload
            className="mt-1"
            accept={ACCEPTED_IMAGE_TYPES.join(",")}
            customRequest={customRequest}
            showUploadList={false}
            multiple={false}
          >
            <Button
              type="primary"
              size="small"
              icon={<Icon icon="icon-park-outline:camera" />}
              className="px-3 rounded-sm"
            >
              Tải lên
            </Button>
          </Upload>
        </ImgCrop>
      </div>
    </div>
  );
};

export default React.memo(AvatarUpload);
