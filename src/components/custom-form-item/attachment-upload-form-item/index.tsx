import React from "react";
import { UploadOutlined } from "@ant-design/icons";
import { Button, Form, message, Upload } from "antd";
import type { FormItemProps, UploadFile, UploadProps } from "antd";
import { isString } from "lodash";

import { UploadService, AttachmentService } from "@/services";
import { useMutation } from "@tanstack/react-query";

const service = new UploadService();
const attachmentService = new AttachmentService();

const normFile: FormItemProps["getValueFromEvent"] = (e) => {
  if (Array.isArray(e)) {
    return e;
  }
  return e && e.fileList;
};

const AttachmentUploadFormItem = (
  props: App.Components.Uploader.AttachmentFormItem.Props
) => {
  const { form, userId, ownerType = "USER" } = props;

  const files = Form.useWatch("attachments", form);

  const uploadMutation = useMutation({
    mutationFn: (fileUpload: File) => {
      return service
        .uploadAttachment(fileUpload as File, userId as string, ownerType)
        .then((res) => res.data);
    }
  });

  const attachmentMutation = useMutation({
    mutationFn: (files: string[]) => {
      return Promise.all(
        files.map((file) =>
          attachmentService.getAttachment(file).then((res) => res.data)
        )
      );
    },
  });

  const customRequest: UploadProps["customRequest"] = (options) => {
    const { onSuccess, onError, file } = options;

    uploadMutation.mutate(file as File, {
      onSuccess: (res) => {
        onSuccess?.(res);
      },
      onError: (error) => {
        onError?.(error);
      }
    });
  };

  React.useEffect(() => {
    if (!files) return;

    const stringFiles = files.filter((file: string | UploadFile) =>
      isString(file)
    ) as string[];

    if (!stringFiles.length) return;

    attachmentMutation.mutate(stringFiles, {
      onSuccess: (res) => {
        const initialFileList = res.map(
          (file) =>
            ({
              uid: file?._id,
              size: file?.size,
              name: file?.title,
              type: file?.type,
              url: file?.url,
              response: file
            } as UploadFile)
        );
        form.resetFields(["attachments"]);
        form.setFieldValue("attachments", initialFileList);
      },
      onError: () => {
        form.resetFields(["attachments"]);
        message.error("Lỗi khi lấy tài liệu");
      }
    });
  }, [files, form]);

  return (
    <Form.Item
      name="attachments"
      valuePropName="fileList"
      label="Tài liệu đính kèm"
      getValueFromEvent={normFile}
    >
      <Upload
        customRequest={customRequest}
        showUploadList={{
          extra: ({ size = 0 }) => (
            <span style={{ color: "#cccccc" }}>
              ({(size / 1024 / 1024).toFixed(2)}MB)
            </span>
          ),
          showDownloadIcon: true,
          downloadIcon: "Download",
          showRemoveIcon: true
        }}
      >
        <Button icon={<UploadOutlined />}>Chọn tệp</Button>
      </Upload>
    </Form.Item>
  );
};

export default React.memo(AttachmentUploadFormItem);
