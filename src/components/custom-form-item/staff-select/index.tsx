import React from "react";
import { Select, SelectProps } from "antd";
import { useQuery } from "@tanstack/react-query";
import { StaffService } from "@/services";

const service = new StaffService();

const StaffSelect = (props: SelectProps) => {
  const { data: staffs } = useQuery({
    queryKey: ["staffs"],
    queryFn: () => service.getStaffs().then(res => res.data).then(res => res?.results.map(staff => ({
      label: staff.user.name,
      value: staff._id
    }))),
  });

  return <Select options={staffs || []} {...props} />;
};

export default React.memo(StaffSelect);