import React from "react";
import { Select, SelectProps } from "antd";
import { MaritalStatusMap } from "@/constants";
import { convertMapToArray } from "@/constants/utils";

const MaritalStatusOptions = convertMapToArray(MaritalStatusMap);

type MaritalStatusSelectProps = Omit<SelectProps, "options">;

const MaritalStatusSelect = (props: MaritalStatusSelectProps) => {
  const { ...restProps } = props;

  return <Select options={MaritalStatusOptions} {...restProps} />;
};

export default React.memo(MaritalStatusSelect) as React.ComponentType<MaritalStatusSelectProps>;
