import { Avatar } from "@/components";
import { cn } from "@/utils";
import React from "react";

const AvatarGroup = React.forwardRef<
  App.Components.AvatarGroup.Ref,
  App.Components.AvatarGroup.Props
>((props, ref) => {
  const {
    className,
    data,
    size = "large",
    maxAvatarShownCount = 3,
    ...restProps
  } = props;

  const [shallowData, setShallowData] =
    React.useState<App.Components.AvatarGroup.Avatar[]>(data);

  const addAvatars = (avatars: App.Components.AvatarGroup.Avatar[]) => {
    setShallowData([...shallowData, ...avatars]);
  };

  const removeAvatars = (avatars: App.Components.AvatarGroup.Avatar[]) => {
    setShallowData(shallowData.filter((avatar) => !avatars.includes(avatar)));
  };

  React.useImperativeHandle(
    ref,
    () => ({
      addAvatars,
      removeAvatars
    }),
    []
  );

  React.useEffect(() => {
    setShallowData(data);
  }, [data]);

  return (
    <div className={cn("flex items-center min-h-6", className)} {...restProps}>
      {shallowData.map((avatar, index) => {
        if (index >= maxAvatarShownCount) {
          return null;
        }

        console.log(size);

        return (
          <div
            className={cn(
              "relative w-7",
              size === "default" ? "w-5" : "",
              size === "small" ? "w-4" : ""
            )}
            key={avatar.id}
          >
            <Avatar
              size={size}
              avatarDisplayName={avatar.name}
              avatar={avatar.avatar}
              className="cursor-pointer absolute top-1/2 -translate-y-1/2 rounded-full border border-gray-50 border-solid"
            />
          </div>
        );
      })}
      {shallowData.length > maxAvatarShownCount && (
        <div
          className={cn(
            "relative w-7",
            size === "default" ? "w-5" : "",
            size === "small" ? "w-3" : ""
          )}
        >
          <Avatar
            size={size}
            avatarDisplayName={`${shallowData.length - maxAvatarShownCount} +`}
            className="cursor-pointer absolute top-1/2 -translate-y-1/2 rounded-full border border-gray-50 border-solid"
          />
        </div>
      )}
    </div>
  );
});

AvatarGroup.displayName = "AvatarGroup";

export default AvatarGroup;
