declare namespace App.Components.AvatarGroup {
  export interface Avatar {
    id: string;
    name: string;
    avatar?: string;
  }

  export interface Props
    extends Omit<React.HTMLAttributes<HTMLDivElement>, "ref"> {
    data: Avatar[];
    maxAvatarShownCount?: number;
    size?: App.Components.Avatar.AvatarProps["size"];
  }

  export interface Ref {
    addAvatars: (avatars: Avatar[]) => void;
    removeAvatars: (avatars: Avatar[]) => void;
  }
}
