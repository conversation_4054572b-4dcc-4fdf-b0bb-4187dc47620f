import React from 'react'
import type { Met<PERSON>, StoryObj } from "@storybook/react";
import Popover from "."
import Button from '../button'

const meta = {
  title: 'Atoms/Popover',
  component: Popover
} satisfies Meta<typeof Popover>;
export default meta
type Story = StoryObj<typeof meta>

const content = (
  <div>
    <p>Content</p>
    <p>Content</p>
  </div>
)

const Example = React.memo(() => {
  return (
    <div className="flex gap-3 justify-center">
      <Popover content={content} title="Title" trigger="hover">
        <Button>Hover me</Button>
      </Popover>
      <Popover content={content} title="Title" trigger="focus">
        <Button>Focus me</Button>
      </Popover>
      <Popover content={content} title="Title" trigger="click">
        <Button>Click me</Button>
      </Popover>
    </div>
  )
})
export const Default: Story = {
  args: {},
  render: () => <Example />
}

