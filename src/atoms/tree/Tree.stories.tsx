import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import Tree from ".";

const treeData = [
  {
    title: "parent 1",
    key: "0-0",
    children: [
      {
        title: "parent 1-0",
        key: "0-0-0",
        disabled: true,
        children: [
          {
            title: "leaf",
            key: "0-0-0-0",
            disableCheckbox: true
          },
          {
            title: "leaf",
            key: "0-0-0-1"
          }
        ]
      },
      {
        title: "parent 1-1",
        key: "0-0-1",
        children: [
          {
            title: <span style={{ color: "#1677ff" }}>sss</span>,
            key: "0-0-1-0"
          }
        ]
      }
    ]
  }
];

const meta = {
  title: "Atoms/Tree",
  component: Tree,
  args: {
    treeData: treeData,
    onSelect: (selectedKeys, info) => {
      console.log("selected", selectedKeys, info);
    },
    onCheck: (checkedKeys, info) => {
      console.log("onCheck", checkedKeys, info);
    }
  }
} satisfies Meta<typeof Tree>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    checkable: true,
    defaultExpandedKeys: ["0-0-0", "0-0-1"],
    defaultSelectedKeys: ["0-0-1"],
    defaultCheckedKeys: ["0-0-0", "0-0-1"]
  }
};
