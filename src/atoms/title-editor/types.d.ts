declare namespace App.Atoms {
  export type TitleEditorProps = {
    editable?: boolean
    value?: string
    onChange?:(value?:string) => void
    onClick?:(e: MouseEventHandler ) => void
    onBlur?:(e: React.FocusEvent<HTMLInputElement, Element>) => void
    onPressEnter?:(e: KeyboardEventHandler) => void
    extraToolbar?: ReactNode
    containerClass?: string
    titleClass?: string
    inputClass?: string
  }
  export type TitleEditorRef = {
    getValue(): string
    setValue(value?: string): void
    showEdit(state?: boolean): void
    blur(): void
  }
}