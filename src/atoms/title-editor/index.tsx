import React, { FocusEventHandler } from 'react'

import type { InputRef } from 'antd'
import { Input, Typography } from '@/atoms'

const TitleEditor = React.forwardRef<
  App.Atoms.TitleEditorRef,
  App.Atoms.TitleEditorProps
>((props, ref) => {
  const {
    editable,
    value,
    onChange,
    onClick,
    onBlur,
    onPressEnter,
    extraToolbar,
    containerClass,
    titleClass,
    inputClass
  } = props

  const inputTitleRef = React.useRef<InputRef>(null)
  const [data, setData] = React.useState<string>(value ?? '')
  const [isEditing, setIsEditing] = React.useState<boolean>()

  const handleEdit = React.useCallback<React.MouseEventHandler<HTMLElement>>((e) => {
    setIsEditing(true)
    onClick?.(e)
  }, [])

  const handleAction = React.useCallback((inputValue?: string) => {
    const newValue = (!!inputValue ? inputValue : value) ?? ''

    setData(newValue)
    setIsEditing(false)

    onChange?.(newValue)
  }, [onChange, value])

  const handleInputBlur = React.useCallback<FocusEventHandler<HTMLInputElement>>((e) => {
    handleAction(e.target.value)
    onBlur?.(e)
  }, [onBlur, handleAction])

  const handleInputPressEnter = React.useCallback<React.KeyboardEventHandler<HTMLInputElement>>((e) => {
    const inputValue = (e.target as HTMLInputElement).value
    handleAction(inputValue)
    onPressEnter?.(e)
  }, [handleAction, onPressEnter])

  const handleInputChange = React.useCallback<React.ChangeEventHandler<HTMLInputElement>>((e) => {
    setData(e.target.value)
  }, [])

  //Ref
  const setValue = React.useCallback((newValue: string) => {
    handleAction(newValue)
  }, [handleAction])

  React.useEffect(() => {
    if (isEditing) {
      setTimeout(() => {
        inputTitleRef.current?.focus()
        inputTitleRef.current?.select()
      }, 10)
    }
  }, [isEditing])

  React.useImperativeHandle(ref, () => ({
    getValue: () => data,
    setValue,
    showEdit: (state?: boolean) => {
      if (editable) {
        if (state === undefined) {
          setIsEditing(oldState => !oldState)
        } else {
          setIsEditing(state)
        }
      }
    },
    blur: () => inputTitleRef.current?.blur()
  }), [data])

  return (
    <div className={containerClass}>
      {editable && isEditing ? (
          <Input
            ref={inputTitleRef}
            value={data}
            autoFocus
            onBlur={handleInputBlur}
            onChange={handleInputChange}
            onPressEnter={handleInputPressEnter}
            className={inputClass}
          />
        ) : (
          <div className={'flex gap-5 justify-between items-center'}>
            <Typography.Title
              level={2}
              className={titleClass}
              onClick={handleEdit}
            >{value}</Typography.Title>
            {extraToolbar}
          </div>
        )}
    </div>
  )
})

TitleEditor.displayName = 'TitleEditor'
export default React.memo(TitleEditor)