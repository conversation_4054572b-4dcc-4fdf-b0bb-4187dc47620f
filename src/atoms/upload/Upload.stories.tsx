import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import <PERSON><PERSON> from "../button";
import Upload from ".";
import { fn } from "@storybook/test";

const meta = {
  title: "Atoms/Upload",
  component: Upload,
  tags: ["autodocs"],
  argTypes: {
    listType: {
      options: ["text", "picture", "picture-card", "picture-circle"],
      control: { type: "radio" }
    }
  },
  args: {
    onChange: fn(),
    onDrop: fn(),
    onDownload: fn(),
    onPreview: fn(),
    onRemove: fn()
  }
} satisfies Meta<typeof Upload>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: <Button>Upload</Button>
  }
};
