import React from 'react'
import type { Meta, StoryObj } from '@storybook/react'
import Badge from './index'
import Avatar from '../avatar'

const meta = {
  title: 'Atoms/Badge',
  component: Badge
} satisfies Meta<typeof Badge>

export default meta
type Story = StoryObj<typeof Badge>

const BasicComponent = React.memo(() => {
  return (
    <Badge count={5}>
      <Avatar shape="square" size="large" />
    </Badge>
  )
})

const OverflowCountComponent = React.memo(() => {
  return (
    <Badge count={99} overflowCount={10}>
      <Avatar shape="square" size="large" />
    </Badge>
  )
})

export const Default: Story = {
  args: {},
  render: (props) => <BasicComponent {...props}/>
}

export const OverflowCount: Story = {
  args: {},
  render: (props) => <OverflowCountComponent {...props}/>
}