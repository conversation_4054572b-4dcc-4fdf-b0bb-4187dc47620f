import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import Tooltip from ".";
import Button from "../button";

const meta = {
  title: "Atoms/Tooltip",
  component: Tooltip,
  parameters: {
    layout: "centered"
  },
  argTypes: {
    placement: {
      options: [
        "top",
        "right",
        "bottom",
        "left",
        "topRight",
        "topLeft",
        "bottomRight",
        "bottomLeft",
        "rightTop",
        "rightBottom",
        "leftTop",
        "leftBottom"
      ],
      control: "select"
    }
  },
  args: {
    children: <Button>Hover here</Button>
  }
} satisfies Meta<typeof Tooltip>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    title: "Promp text"
  }
};

export const BottomPlacement: Story = {
  name: "Placement: Bottom",
  args: {
    title: "Promp text",
    placement: "bottom"
  }
};
