import type { Meta, StoryObj } from "@storybook/react";
import InputNumber from ".";

const meta = {
  title: "Atoms/InputNumber",
  component: InputNumber,
  args: {
    onChange: (value) => {
      console.log("changed", value);
    }
  }
} satisfies Meta<typeof InputNumber>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    min: 1,
    max: 10,
    defaultValue: 3
  }
};
