import BaseComponent from "@/components/base";
import React from "react";
import View from "./View";

// MenuItemDetail class component extending BaseComponent
export class MenuItemDetail extends BaseComponent {
    view: React.ReactNode; // The view for the MenuItemDetail component
    #ref: React.RefObject<App.Components.MenuItemDetailRef>; // Ref for accessing the component's DOM or methods

    // Constructor for MenuItemDetail
    constructor(
        app: App.Application, // Application instance
        props: App.Components.MenuItemDetailProps // Properties for the menu item
    ) {
        // Call the constructor of the parent BaseComponent class
        super(app);

        // Create a reference to the MenuItemDetail component
        this.#ref = React.createRef();

        // Create the React element for the View component, passing down the props and the created ref
        this.view = React.createElement(View, {
            ...(props || {}), // Spread the props if they exist, fallback to an empty object
            ref: this.#ref, // Attach the ref to the View component
        });
    }
}
