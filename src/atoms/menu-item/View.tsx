import React, { useState, useCallback, memo } from "react";
import { Icon } from "@/atoms";
import { Text } from "@/atoms/typography/text";
import { cn } from "@/utils";
import { theme } from "antd";

// MenuItemView component for rendering a menu item with possible subitems
const MenuItemView = React.forwardRef<
    App.Components.MenuItemDetailRef, // Type for the ref
    App.Components.MenuItemDetailProps // Props for the menu item
>(({ item, index, onClick }, _) => {
    // Destructure item properties with defaults
    const {
        type = "item", // Menu item type ("item", "subitem", "group", or "divider")
        title, // Menu item title
        icon, // Menu item icon
        subIcon, // Optional sub icon
        defaultOpen = false, // Whether the item is open by default
        children = [], // Subitems or children of the menu item
    } = item;

    // Access Ant Design theme token for styling
    const { token } = theme.useToken();

    // State to manage the open/close state of the menu item (for groups and subitems)
    const [isOpen, setIsOpen] = useState(defaultOpen);

    // Callback to handle the click event on the menu item
    const handleClick = useCallback(() => {
        setIsOpen(!isOpen); // Toggle the open/close state
        if (type === "item") {
            onClick(item); // Call onClick if the item is of type "item"
        }
    }, [isOpen, item, type, onClick]); // Dependencies: isOpen, item, type, and onClick

    // Function to render the main icon for the menu item
    const renderMainIcon = useCallback(() => {
        if (type === "group" && children.length > 0) {
            // If the item is a group and has children, render a toggle icon
            return <Icon icon={`formkit:${isOpen ? "down" : "right"}`} />;
        }
        if (type !== "group" && icon) {
            // Render the menu item icon if it's not a group
            return <span>{icon}</span>;
        }
        return null; // Return null if no icon is to be rendered
    }, [children, isOpen, type, icon]); // Dependencies: children, isOpen, type, and icon

    // Function to render the child items (subitems) if they exist and the menu item is open
    const renderChildren = () => {
        if (children.length > 0 && isOpen) {
            return (
                <div
                    className={`${
                        type === "subitem" ? "ml-5 border-l" : ""
                    } mb-1`}
                    style={{
                        borderColor:
                            type === "subitem" ? token.colorBorder : undefined,
                    }}
                >
                    {children.map((child, index) => (
                        // Render each child as a MenuItemView component
                        <MenuItemView
                            key={index}
                            index={index}
                            item={child}
                            onClick={onClick}
                        />
                    ))}
                </div>
            );
        }
        return null; // Return null if there are no children or the menu item is closed
    };

    // If the item is a "divider", render a horizontal line instead of a menu item
    if (type === "divider") {
        return (
            <div
                className="w-full"
                style={{
                    backgroundColor: token.colorSplit, // Divider color from theme token
                    height: "1px", // Set height of the divider
                }}
            />
        );
    }

    return (
        <div key={index}>
            {/* Main Menu Item */}
            <div
                className={cn(
                    `flex items-center justify-between p-3 rounded-lg cursor-pointer gap-5`
                )}
                onClick={handleClick} // Handle the click event
            >
                <div className="flex items-center gap-3">
                    {/* Render the main icon and title */}
                    {renderMainIcon()}
                    <Text>{title}</Text>
                    {/* Render the toggle icon if the item has children and is not a group */}
                    {type !== "group" && children.length > 0 && (
                        <Icon icon={`formkit:${isOpen ? "down" : "right"}`} />
                    )}
                </div>
                {subIcon && subIcon} {/* Render the sub-icon if it exists */}
            </div>

            {/* Render the child items if they exist */}
            {renderChildren()}
        </div>
    );
});

// Set the display name for debugging purposes
MenuItemView.displayName = "MenuItemView";

// Export the component wrapped in memo to optimize rendering
export default memo(MenuItemView);
