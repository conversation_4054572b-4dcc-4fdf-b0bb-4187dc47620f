import React from "react";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import Popconfirm from "."
import Button from '../button'

const meta = {
  title: 'Atoms/Popconfirm',
  component: Popconfirm
} satisfies Meta<typeof Popconfirm>;
export default meta
type Story = StoryObj<typeof meta>

const Example = React.memo(() => {
  return (
    <div className="w-[500px] flex justify-center">
      <Popconfirm
        title="Delete the task"
        description="Are you sure to delete this task?"
        // onConfirm={confirm}
        // onCancel={cancel}
        okText="Yes"
        cancelText="No"
      >
        <Button danger>Delete</Button>
      </Popconfirm>
    </div>
  )
})
export const Default: Story = {
  args: {
    title: 'Popconfirm'
  },
  render: () => <Example />
}

