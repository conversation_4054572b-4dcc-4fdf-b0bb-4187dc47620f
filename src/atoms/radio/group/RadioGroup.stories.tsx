import React from 'react'
import type { Meta, StoryObj } from "@storybook/react"
import Radio from ".."
import type { RadioChangeEvent } from 'antd';

const { Group } = Radio

const meta = {
  title: 'Atoms/Radio:Group',
  component: Group
} satisfies Meta<typeof Group>;
export default meta
type Story = StoryObj<typeof meta>

const Example = React.memo(() => {
  const [value, setValue] = React.useState(1);

  const onChange = (e: RadioChangeEvent) => {
    console.log('radio checked', e.target.value);
    setValue(e.target.value);
  };

  return (
    <Radio.Group onChange={onChange} value={value}>
      <Radio value={1}>A</Radio>
      <Radio value={2} disabled>B</Radio>
      <Radio value={3}>C</Radio>
      <Radio value={4}>D</Radio>
    </Radio.Group>
  )
})
export const Default: Story = {
  args : {},
  render : () => <Example/>
}

