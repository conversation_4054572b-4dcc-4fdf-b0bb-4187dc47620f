declare namespace Atoms {
  type AntdRadioProps = import('antd').RadioProps
  type AntdRadioGroupProps = import('antd').RadioGroupProps

  export interface RadioGroupProps extends AntdRadioGroupProps {}

  export interface RadioProps extends AntdRadioProps {}
}

declare namespace App.Atoms {
  type AntdRadioProps = import('antd').RadioProps
  type AntdRadioGroupProps = import('antd').RadioGroupProps

  export interface RadioGroupProps extends AntdRadioGroupProps {}

  export interface RadioProps extends AntdRadioProps {}
}