import { Breadcrumb as AntBreadcrumb } from 'antd'
import { AnyObject } from 'antd/es/_util/type'
import { BreadcrumbItemType, BreadcrumbSeparatorType } from 'antd/es/breadcrumb/Breadcrumb'
import { useCallback, useMemo } from 'react'
import './style.css'

interface ParamType extends Object {
  [key: number | string]: Object
}

const Breadcrumb = (props: Atoms.BreadcrumbProps) => {
  const { params, itemRender, items, className, ...restProps } = props

  const myParams = useMemo(() => {
    const myData: ParamType  = {}
    items?.forEach((item, _key) => {
      const { key, icon, numNotify } = item
      const __key = key !== undefined ? key.toString() : _key.toString()
      myData[__key] = { icon, numNotify }
    })

    return {
      ...(params || {}),
      myData
    }
  }, [params, items])

  const myItems = useMemo(() => {
    return items?.map(item => {
      const { icon, numNotify, ...restProps } = item || {}

      return restProps
    })
  }, [items])

  const myItemRender = useCallback((route: Partial<BreadcrumbItemType & BreadcrumbSeparatorType>, myParams: AnyObject, routes: Partial<BreadcrumbItemType & BreadcrumbSeparatorType>[], paths: string[]) => {
    if (itemRender) {
      return itemRender(route, myParams, routes, paths)
    }
    
    // co route.key >> tim trong params >> params.myData[route.key]
    // numNotify, icon
    const key = route.key?.toString() ?? ''
    
    const icon = myParams.myData[key].icon ?? ''
    const numNotify = myParams.myData[key].numNotify ?? ''
    const href = route.href ?? ''

    return (
      href ? (
        <a href={href} target='_blank' className={route.className ?? ''}>{icon} {route.title} <span className='numNotiy'>{numNotify}</span></a>
      ) : (<div className={route.className ?? ''}>{icon} {route.title} <span className='numNotiy'>{numNotify}</span></div>)
      
    )
  }, [itemRender])

  return (
    <AntBreadcrumb<ParamType>
      className={`custom-breadcrumb ${className}`}
      separator=""
      {...restProps}
      items={myItems}
      params={myParams}
      itemRender={myItemRender}
    />)
}

export default Breadcrumb