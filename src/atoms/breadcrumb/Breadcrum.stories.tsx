import type { <PERSON>a, StoryObj } from '@storybook/react'
import Breadcrumb from "./index"
import { Icon } from '@iconify/react'

const meta = {
    title: 'Atoms/Breadcrumb',
    component: Breadcrumb,
} satisfies Meta<typeof Breadcrumb>

export default meta
type Story = StoryObj<typeof meta>

const items: Atoms.BreadcrumbProps['items'] = [
    { 
      key: 1,
      numNotify: 4,
      title: <span>Sonsac</span>,
      icon: <Icon icon="icon-park-outline:home" className='text-gray-light'/>,
      className: ''
    },
    { 
      key: 2,
      numNotify: 4,
      title: 'Report',
      icon: <Icon icon="icon-park-outline:align-text-right-one" className='text-gray-light'/>
    },
    { 
      key: 3,
      numNotify: 4,
      title: 'Company by Country',
      href: 'https://google.com.vn/'
    }
  ]
 

export const Default: Story = {
    args: {
        items: items,
        className: ''
    }
}