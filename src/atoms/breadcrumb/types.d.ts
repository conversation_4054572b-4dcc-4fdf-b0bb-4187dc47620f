declare namespace Atoms {
    type AntBreadcrumbItemType = import('antd').BreadcrumbItemType
    type AntItemType = import('antd/es/breadcrumb/Breadcrumb').ItemType

    export interface BreadcrumbItemType extends AntBreadcrumbItemType {
        icon?: React.ReactNode,
        numNotify?: Number
    }

    export type ItemType = Partial <BreadcrumbItemType & AntItemType>

    type AntBreadcrumbProps = import('antd').BreadcrumbProps

    export interface BreadcrumbProps extends Omit<AntBreadcrumbProps, 'items'> {
        items: ItemType[]
    }
}