import type { Meta, StoryObj } from "@storybook/react";
import TimePicker from ".";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";

dayjs.extend(customParseFormat);

const meta = {
  title: "Atoms/TimePicker",
  component: TimePicker,
  argTypes: {
    size: {
      options: ["large", "middle", "small"],
      control: "radio"
    },
    status: {
      options: ["error", "warning", "success", "validating"],
      control: "radio"
    },
    use12Hours: {
      control: "boolean"
    },
    variant: {
      options: ["outlined", "borderless", "filled"],
      control: "radio"
    },
    autoFocus: {
      control: "boolean"
    },
    allowClear: {
      control: "boolean"
    },
    changeOnScroll: {
      control: "boolean"
    },
    disabled: {
      control: "boolean"
    },
    hideDisabledOptions: {
      control: "boolean"
    },
    inputReadOnly: {
      control: "boolean"
    },
    needConfirm: {
      control: "boolean"
    },
    open: {
      control: "boolean"
    },
    placement: {
      options: ["topRight", "topLeft", "bottomRight", "bottomLeft"],
      control: "radio"
    }
  },
  args: {}
} satisfies Meta<typeof TimePicker>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    defaultOpenValue: dayjs("00:00:00", "HH:mm:ss"),
    onChange: (time, timeString) => {
      console.log(time, timeString);
    }
  }
};
