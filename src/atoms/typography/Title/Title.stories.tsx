import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import Typography from "..";

const { Title } = Typography;

const meta = {
  title: "Atoms/Typography:Title",
  component: Title,
  tags: ["autodocs"],
  argTypes: {
    level: {
      options: [1, 2, 3, 4, 5],
      control: "radio"
    },
    italic: {
      control: "boolean"
    },
    underline: {
      control: "boolean"
    },
    mark: {
      control: "boolean"
    },
    disabled: {
      control: "boolean"
    },
    delete: {
      control: "boolean"
    },
    code: {
      control: "boolean"
    },
    type: {
      options: ["secondary", "success", "warning", "danger"],
      control: "radio"
    }
  }
} satisfies Meta<typeof Title>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  name: "Heading 1 (Default)",
  args: {
    children: "Super Idol",
  }
};
export const H2: Story = {
  name: "Heading 2",
  args: {
    children: "Super Idol",
    level: 2
  }
};
export const H3: Story = {
  name: "Heading 3",
  args: {
    children: "Super Idol",
    level: 3
  }
};
export const H4: Story = {
  name: "Heading 4",
  args: {
    children: "Super Idol",
    level: 4
  }
};
export const H5: Story = {
  name: "Heading 5",
  args: {
    children: "Super Idol",
    level: 5
  }
};
export const Italic: Story = {
  name: "Italic Style",
  args: {
    children: "Italic Style",
    italic: true
  }
};
export const Underline: Story = {
  name: "Underline Style",
  args: {
    children: "Underline Style",
    underline: true
  }
};
export const Disabled: Story = {
  name: "Underline Style",
  args: {
    children: "Underline Style",
    underline: true
  }
};
export const Delete: Story = {
  name: "Delete Style",
  args: {
    children: "Delete Style",
    delete: true
  }
};
export const Mark: Story = {
  name: "Mark Style",
  args: {
    children: "Mark Style",
    mark: true
  }
};
export const Content: Story = {
  name: "Content type style",
  args: {
    children: "Content styles",
    type: "success"
  }
};
