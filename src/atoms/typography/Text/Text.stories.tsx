import type { <PERSON>a, StoryObj } from "@storybook/react";
import Typography from "..";

const { Text } = Typography;
const meta = {
  title: "Atoms/Typography:Text",
  component: Text,
  tags: ["autodocs"],
  argTypes: {
    type: {
      options: ["subtitle", "secondary", "warning", "danger", "success"],
      control: "radio"
    }
  }
} satisfies Meta<typeof Text>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: "Super Idol"
  }
};

export const Subtitle: Story = {
  args: {
    children: "Super Idol",
    type: "subtitle"
  }
};

export const Danger: Story = {
  args: {
    children: "Super Idol",
    type: "danger"
  }
};

export const Secondary: Story = {
  args: {
    children: "Super Idol",
    type: "secondary"
  }
};

export const Success: Story = {
  args: {
    children: "Super Idol",
    type: "success"
  }
};

export const Warning: Story = {
  args: {
    children: "Super Idol",
    type: "warning"
  }
};
