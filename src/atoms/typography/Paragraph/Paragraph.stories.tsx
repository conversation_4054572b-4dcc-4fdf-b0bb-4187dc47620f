import type { Meta, StoryObj } from "@storybook/react";
import Typography from "..";

const { Paragraph } = Typography;

const meta = {
  title: "Atoms/Typography:Paragraph",
  component: Paragraph,
  tags: ["autodocs"]
} satisfies Meta<typeof Paragraph>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children:
      "Super Idol Sed tortor dignissim condimentum scelerisque laoreet diam turpis consectetuer feugiat mollis nisi nullam vitae volutpat accumsan orci aptent ante vivamus risus curabitur platea vel vestibulum magna lectus sem habitant etiam euismod facilisi elementum metus quam lobortis fringilla ultrices penatibus elit aenean ligula auctor sollicitudin parturient neque"
  }
};
