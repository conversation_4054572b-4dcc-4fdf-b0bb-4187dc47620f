/// <reference types="react" />

declare namespace Atoms {
  type AntdTypography = import('antd/es/typography').TypographyProps
  type AntdTextProps = import('antd/es/typography/Text').TextProps
  type BaseType = import('antd/es/typography/Base').BaseType
  type AntdTitleProps = import('antd/es/typography/Title').TitleProps

  export interface TextProps extends Omit<AntdTextProps, 'type'> {
    type?: BaseType | 'subtitle'
  }

  export interface TypographyProps extends Omit<AntdTypography, 'Text'> {
    Text?: TextProps
  }
}