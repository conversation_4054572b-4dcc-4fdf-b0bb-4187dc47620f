import { Typography } from "antd";

import { cn } from "../../utils";
import React from "react";

const SUB_TITLE_CLASSES = "text-[11px]";

export const Text: React.ForwardRefExoticComponent<
  Atoms.TextProps & React.RefAttributes<HTMLSpanElement>
> = React.forwardRef<HTMLSpanElement, Atoms.TextProps>(
  ({ type, className, ...restProps }: Atoms.TextProps, ref) => {
    if (type === "subtitle") {
      return (
        <Typography.Text
          ref={ref}
          className={cn(SUB_TITLE_CLASSES, className)}
          {...restProps}
        />
      );
    }

    return <Typography.Text className={cn('', className)} ref={ref} type={type} {...restProps} />;
  }
);
