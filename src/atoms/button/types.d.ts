declare namespace Atoms {
  type AntdButtonProps = import('antd').ButtonProps

  export interface ButtonProps extends Omit<AntdButtonProps, "type"> {
    type?: ButtonProps["type"] | "secondary";
  };
}
declare namespace App.Atoms {
  type AntdButtonProps = import('antd').ButtonProps

  export interface ButtonProps extends Omit<AntdButtonProps, "type"> {
    type?: ButtonProps["type"] | "secondary";
  };
}