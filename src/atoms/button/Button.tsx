import { Button as AntdButton } from "antd";
import { cn } from "../../utils";

const SECONDARY_CLASSES = "bg-secondary";

export const Button = ({
  type = "default",
  children,
  className,
  size,
  ...props
}: Atoms.ButtonProps) => {

  return (
    <AntdButton
      type={type === "secondary" ? "default" : type}
      size={size}
      className={cn(
        "",
        type === "secondary" ? SECONDARY_CLASSES : "",
        className
      )}
      {...props}
    >
      {children}
    </AntdButton>
  );
};
