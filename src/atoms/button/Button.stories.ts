import type { <PERSON>a, StoryObj } from '@storybook/react';
import { But<PERSON> } from "./Button";
import { fn } from '@storybook/test';

const meta = {
  title: 'Atom/Button',
  component: Button,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'centered',
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['autodocs'],
  argTypes: {
    size: {
      control: 'radio',
      options: ['small', 'middle', 'large']
    }
  },
  args: {
    onClick: fn()
  }
} satisfies Meta<typeof Button>

export default meta;
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    type: 'default',
    children: 'Default',
  }
}

export const Primary: Story = {
  args: {
    type: 'primary',
    children: 'Primary',
  }
}

export const Secondary: Story = {
  args: {
    type: 'secondary',
    children: 'Secondary',
  }
}

export const Link: Story = {
  args: {
    type: 'link',
    children: 'Link',
  }
}

export const Dashed: Story = {
  args: {
    type: 'dashed',
    children: 'Dashed',
  }
}

export const Text: Story = {
  args: {
    type: 'text',
    children: 'Text',
  }
}

export const Large: Story = {
  args: {
    type: 'default',
    children: 'Large',
    size: 'large'
  }
}

export const Small: Story = {
  args: {
    type: 'default',
    children: 'Small',
    size: 'small'
  }
}

