import {
  forwardRef,
  useImperativeHandle,
  useState,
  useCallback,
  useRef,
} from "react";
import { Image } from "@/atoms";
import { Mentions as AMentions } from "antd";
import { MentionsRef } from "antd/es/mentions";
import { Text } from "../typography/text";

const { Option } = AMentions;

export const Mentions = forwardRef<Atoms.MentionsRef, Atoms.MentionsProps>(
  ({ dataSource, placeholder, onChange, onSelect, ...props }, ref) => {
    const [value, setValue] = useState<string>("");
    const mentionsRef = useRef<MentionsRef>(null);

    useImperativeHandle(
      ref,
      () => ({
        clear: () => setValue(""),
      }),
      []
    );

    const handleChange = useCallback(
      (val: string) => {
        setValue(val);
        onChange?.(val);
      },
      [onChange]
    );

    const handleSelect = useCallback(
      (option: any, prefix: string) => {
        const selectedOption = dataSource.find(
          (item) => item.value === option.value
        );
        if (selectedOption) {
          onSelect?.(selectedOption, prefix);
        }
      },
      [onSelect, dataSource]
    );

    return (
      <AMentions
        {...props}
        ref={mentionsRef}
        value={value}
        onChange={handleChange}
        onSelect={handleSelect}
      >
        {dataSource.map(( item) => (
          <Option key={item.value} value={item.value}>
            <div className="flex items-center gap-[10px]">
              {item.avatar && (
                <Image
                  src={item.avatar}
                  alt={item.label}
                  width={24}
                  height={24}
                />
              )}
              <Text type="subtitle">{item.label}</Text>
            </div>
          </Option>
        ))}
      </AMentions>
    );
  }
);
