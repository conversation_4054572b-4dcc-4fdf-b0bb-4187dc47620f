import type { <PERSON>a, StoryObj } from "@storybook/react";
import Mentions from ".";

const options = [
  {
    value: "afc163",
    label: "afc163",
  },
  {
    value: "zombieJ",
    label: "zombieJ",
  },
  {
    value: "yesmeck",
    label: "yesmeck",
  },
];

const meta = {
  title: "Atoms/Mentions",
  component: Mentions,
  args: {
    onChange: (value: string) => {
      console.log("Change:", value);
    },
    onSelect: (option: Atoms.MentionsOptionProps) => {
      console.log("select", option);
    },
  },
} satisfies Meta<typeof Mentions>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    dataSource: [{ label: "x", value: "x", avatar: "x" }],
  },
};
