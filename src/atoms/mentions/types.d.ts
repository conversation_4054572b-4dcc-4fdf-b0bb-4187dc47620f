declare namespace Atoms {
  type AntdMentionsProps = import("antd").MentionsProps;
  type AntdMentionsOptionProps = import("antd/es/mentions").MentionsOptionProps;

  export interface DataSourceItem {
    value: string;
    label: string;
    avatar?: string;
  }

  export interface MentionsOptionProps extends AntdMentionsOptionProps {}

  export interface MentionsProps extends AntdMentionsProps {
    dataSource: DataSourceItem[];
    onChange?: (value: string) => void;
    onSelect?: (option: DataSourceItem | any, prefix: string) => void;
  }

  export interface MentionsRef {
    clear: () => void;
  }
}
