import type { Meta, StoryObj } from '@storybook/react'
import Alert from './index'

const meta = {
  title: 'Atoms/Alert',
  component: Alert
} satisfies Meta<typeof Alert>

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    message: 'Success Tips',
    description: 'Detailed description and advice about successful copywriting.',
    type: 'success',
    showIcon: true,
    closable: true,
  }
}

export const Warning: Story = {
  args: {
    message: 'Success Tips',
    description: 'Detailed description and advice about successful copywriting.',
    type: 'warning',
    showIcon: true,
    closable: true,
  }
}

export const Info: Story = {
  args: {
    message: 'Success Tips',
    description: 'Detailed description and advice about successful copywriting.',
    type: 'info',
    showIcon: true,
    closable: true,
  }
}

export const Error: Story = {
  args: {
    message: 'Success Tips',
    description: 'Detailed description and advice about successful copywriting.',
    type: 'error',
    showIcon: true,
    closable: true,
  }
}