import type { Meta, StoryObj } from "@storybook/react";
import Switch from "./Switch.tsx";

const meta = {
  title: 'Atoms/Switch',
  component: Switch,
  tags: ['autodocs'],
  argTypes: {}
} satisfies Meta<typeof Switch>;
export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    className: '',
    defaultChecked: true
  }
}
export const Loanding: Story = {
  args: {
    className: '',
    defaultChecked: true,
    loading: true
  }
}
export const Size: Story = {
  args: {
    className: '',
    defaultChecked: true,
    size: 'small'
  }
}
