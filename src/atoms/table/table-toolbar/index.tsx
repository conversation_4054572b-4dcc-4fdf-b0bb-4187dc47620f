import { Button, Icon } from '@/atoms'
import Typography from '@/atoms/typography'
import { cn } from '@/utils'
import React from 'react'

const toolbarActions = [
  {
    icon: 'material-symbols:assignment-add-outline-rounded',
    label: 'Add to list'
  },
  {
    icon: 'material-symbols:stacked-email-outline-rounded',
    label: 'Send mail'
  },
  {
    icon: 'material-symbols:flowchart-outline-sharp',
    label: 'Run workflow'
  },
  {
    icon: 'material-symbols:keyboard-arrow-down-rounded',
    label: 'More',
    isSuffixIcon: true
  }
]

const TableToolbar = React.forwardRef<
  Atoms.Table.TableToolbarRef,
  Atoms.Table.TableToolbarProps
>(({ selectedRows }, _) => (
  <div>
    <div
      className={cn(
        `bg-white fixed bottom-6 left-1/2 transform -translate-x-1/2 min-w-96
         flex items-center gap-4 rounded-lg p-2 shadow-md transition-all`,
        selectedRows?.length ? 'opacity-100' : 'opacity-0'
      )}
    >
      <div className='w-max flex items-center gap-1'>
        <Typography.Text>{selectedRows?.length || 0}</Typography.Text>
        <Typography.Text type='secondary'>Selected</Typography.Text>
      </div>

      {toolbarActions.map((action) => (
        <Button
          key={action.label}
          type='text'
          className={cn(
            `flex items-center transition-all hover:border hover:brightness-95 rounded-lg shadow-md`,
            action.isSuffixIcon ? 'flex-row-reverse' : ''
          )}
        >
          <Icon icon={action.icon} />
          <Typography.Text className='text-sm'>{action.label}</Typography.Text>
        </Button>
      ))}
    </div>
  </div>
))

TableToolbar.displayName = 'TableToolbar'

export default React.memo(TableToolbar)
