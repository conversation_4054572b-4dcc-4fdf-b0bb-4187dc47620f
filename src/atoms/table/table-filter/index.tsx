import React from 'react'
import { Button, Dropdown, Icon, Input, Select } from '@/atoms'
import { useSortableColumns as useFilteraleColumns } from '../table-sorter/hook'
import { useTableFilter } from './hook'
import { useClickOutside } from '@/hooks'
import { cn } from '@/utils'

const TableFilter = React.forwardRef<
  Atoms.Table.TableFilterRef,
  Atoms.Table.TableFilterProps
>(({ onFilterColumn }, ref) => {
  const {
    activeFilterField,
    isFilterSelectOpen,
    showFilterInput,
    setIsFilterSelectOpen,
    handleConditionSelect,
    handleFieldSelect,
    handleClearFilter,
    handleFilterValueChange
  } = useTableFilter({ onFilterColumn })

  const availableColumns = useFilteraleColumns()

  const filterConditions = React.useMemo(() => {
    return [
      {
        value: 'contains',
        label: 'contains'
      },
      {
        value: 'notContains',
        label: 'not contains'
      },
      {
        value: 'startsWith',
        label: 'starts with'
      },
      {
        value: 'endsWith',
        label: 'ends with'
      },
      { value: 'equals', label: 'is' },
      { value: 'notEqual', label: 'is not' },
      {
        value: 'blank',
        label: 'blank'
      },
      { value: 'notBlank', label: 'not blank' }
    ]
  }, [])

  useClickOutside('.table-filter', () => {
    setIsFilterSelectOpen(false)
  })

  const handleOptionSelect = React.useCallback(
    (key: string) => {
      if (key === 'delete-condition') {
        handleClearFilter()
      }
    },
    [handleClearFilter]
  )

  React.useImperativeHandle(ref, () => ({}), [])

  return (
    <div>
      <div className='flex items-center'>
        <div className='table-filter relative'>
          <Button
            type='dashed'
            onClick={() => setIsFilterSelectOpen((prev) => !prev)}
            icon={<Icon icon='mage:filter' />}
            className={cn(
              'h-7',
              activeFilterField &&
                'rounded-tr-none rounded-br-none border-solid'
            )}
          >
            {activeFilterField || 'Filter'}
          </Button>

          <Select.Search
            className='invisible absolute left-0'
            rootClassName='table-filter'
            open={isFilterSelectOpen}
            removeIcon
            showSearch
            options={availableColumns}
            popupClassName='min-w-48'
            onSelect={handleFieldSelect}
          />
        </div>
        <div>
          {activeFilterField && (
            <Select
              placeholder='Select condition'
              options={filterConditions}
              onChange={handleConditionSelect}
              popupClassName='min-w-32'
              className='filter-select h-7'
            />
          )}
        </div>
        <div>
          {showFilterInput && (
            <Input
              className='max-w-32 h-7 text-xs rounded-none'
              placeholder='Enter text...'
              onChange={handleFilterValueChange}
            />
          )}
        </div>
        <div>
          {activeFilterField && (
            <Dropdown
              trigger={['click']}
              onOptionSelect={handleOptionSelect}
              placement='bottomRight'
              options={[
                {
                  label: 'Delete condition',
                  danger: true,
                  key: 'delete-condition',
                  icon: <Icon icon='circum:trash' />
                }
              ]}
            >
              <Button
                icon={<Icon icon='iwwa:option' />}
                className='rounded-tl-none rounded-bl-none border-l-0 h-7'
              />
            </Dropdown>
          )}
        </div>
      </div>
    </div>
  )
})

TableFilter.displayName = 'TableFilter'

export default React.memo(TableFilter)
