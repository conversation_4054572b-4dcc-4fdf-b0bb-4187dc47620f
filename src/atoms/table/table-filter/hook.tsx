import { useDebounce } from '@/hooks'
import React, { ChangeEvent } from 'react'

interface UseTableFilter {
  onFilterColumn?: (filterModel: Atoms.Table.FilterModel) => void
}

export const useTableFilter = ({ onFilterColumn }: UseTableFilter) => {
  const [isFilterSelectOpen, setIsFilterSelectOpen] =
    React.useState<boolean>(false)
  const [filterCriteria, setFilterCriteria] = React.useState<{
    [key: string]: {
      [key: string]: string
    }
  }>({})
  const [showFilterInput, setShowFilterInput] = React.useState<boolean>(false)
  const [activeFilterField, setActiveFilterField] = React.useState<string>('')
  const [filterValue, setFilterValue] = React.useState<string>('')

  const handleFieldSelect = React.useCallback((key: string) => {
    setActiveFilterField(key)
    setIsFilterSelectOpen(false)
    setFilterCriteria((prev) => {
      const newFilterModel = { ...prev }
      if (newFilterModel[key]) {
        delete newFilterModel[key]
      } else {
        newFilterModel[key] = {
          filterType: 'text'
        }
      }
      return newFilterModel
    })
  }, [])

  const handleClearFilter = React.useCallback(() => {
    setIsFilterSelectOpen(false)
    setFilterCriteria({})
    setShowFilterInput(false)
    setActiveFilterField('')
    setFilterValue('')
  }, [])

  const handleConditionSelect = React.useCallback(
    (key: string) => {
      setShowFilterInput(true)
      setFilterCriteria((prev) => {
        const newFilterModel = { ...prev }
        if (newFilterModel[activeFilterField]) {
          newFilterModel[activeFilterField].type = key
        }
        return newFilterModel
      })
    },
    [activeFilterField]
  )

  const handleFilterValueChange = React.useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      setFilterValue(e.target.value)
      setFilterCriteria((prev) => {
        const newFilterModel = { ...prev }
        if (newFilterModel[activeFilterField]) {
          newFilterModel[activeFilterField].filter = e.target.value
        }
        return newFilterModel
      })
    },
    [activeFilterField]
  )

  const debouncedFilterModel = useDebounce(filterCriteria, 200)

  React.useEffect(() => {
    if (Object.keys(debouncedFilterModel).length) {
      onFilterColumn?.(debouncedFilterModel)
    } else {
      onFilterColumn?.({})
    }
  }, [debouncedFilterModel, onFilterColumn])

  return {
    isFilterSelectOpen,
    filterCriteria,
    showFilterInput,
    activeFilterField,
    filterValue,
    debouncedFilterModel,
    handleClearFilter,
    setIsFilterSelectOpen,
    handleFieldSelect,
    handleConditionSelect,
    handleFilterValueChange
  }
}
