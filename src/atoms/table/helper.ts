/**
 * Case-insensitive string comparison for table sorting
 * @param valueA - First value to compare
 * @param valueB - Second value to compare
 * @returns -1 if valueA < valueB, 0 if equal, 1 if valueA > valueB
 */
export const caseInsensitiveComparator = (valueA: string, valueB: string) => {
  if (valueA == null && valueB == null) return 0
  if (valueA == null) return -1
  if (valueB == null) return 1
  return valueA.toLowerCase().localeCompare(valueB.toLowerCase())
}

/**
 * Process data for infinite scrolling table with sorting support
 * @param data - Array of table rows to process
 * @param params - Processing parameters
 * @param params.startRow - Start index of the slice
 * @param params.endRow - End index of the slice
 * @param params.sortModel - Optional sort configuration
 * @param params.filterModel - Optional filter configuration
 * @returns Object containing sliced rows and total count
 */
export const processInfiniteRows = (
  data: Atoms.Table.TableRow[],
  params: Atoms.Table.DataSourceParams
) => {
  let processedData = [...data]

  if (params.sortModel?.length) {
    processedData.sort((a, b) => {
      for (const { colId, sort } of params.sortModel || []) {
        const aValue = String(a[colId]).toLowerCase()
        const bValue = String(b[colId]).toLowerCase()
        const comparison = aValue.localeCompare(bValue)

        if (comparison !== 0) {
          return sort === 'asc' ? comparison : -comparison
        }
      }
      return 0
    })
  }

  if (params.filterModel) {
    processedData = processedData.filter((row) => {
      return Object.entries(params.filterModel || {}).every(
        ([field, condition]) => {
          const value = String(row[field] || '').toLowerCase()
          const filter = String(condition.filter || '').toLowerCase()

          switch (condition.type) {
            case 'contains':
              return value.includes(filter)
            case 'equals':
              return value === filter
            case 'notEqual':
              return value !== filter
            case 'startsWith':
              return value.startsWith(filter)
            case 'endsWith':
              return value.endsWith(filter)
            default:
              return true
          }
        }
      )
    })
  }

  return {
    rows: processedData.slice(params.startRow, params.endRow),
    totalRows: processedData.length
  }
}
