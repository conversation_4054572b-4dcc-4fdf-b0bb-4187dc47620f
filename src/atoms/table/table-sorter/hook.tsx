import React from 'react'
import { SorterVisibility, SortType } from './enum'
import { uniqueId } from 'lodash'
import events from '@/events'
import { TableSortColumnsEvent } from './event'

/**
 * Hook for managing table sorting criteria and state
 * @param onColumnSelect - Callback function when columns are selected for sorting
 * @param onSortRuleClear - Callback function when sorting is cleared
 * @param sorterRef - Reference to the sorter component
 * @returns Object containing sort state and methods to manage sorting
 */
export const useMultiColumnSort = (
  onColumnSelect?: (criteria: Atoms.Table.SortModel[]) => void,
  onSortRuleClear?: () => void
) => {
  const [sortCriteria, setSortCriteria] = React.useState<
    Atoms.Table.SortModel[]
  >([])
  const [sorterVisibility, setSorterVisibility] =
    React.useState<SorterVisibility>(SorterVisibility.NONE)

  const handleSortRuleAdd = React.useCallback(
    (column: string) => {
      const newCriteria: Atoms.Table.SortModel[] = [
        ...sortCriteria,
        { colId: column, sort: SortType.ASC, id: uniqueId() }
      ]

      setSortCriteria(newCriteria)
      setSorterVisibility(SorterVisibility.NONE)
      onColumnSelect?.(newCriteria)
    },
    [sortCriteria, onColumnSelect]
  )

  const handleSortRuleUpdate = React.useCallback(
    (updatedSort: Atoms.Table.SortModel) => {
      const newCriteria = sortCriteria.map((item) =>
        item.id === updatedSort.id ? updatedSort : item
      )
      setSortCriteria(newCriteria)
      onColumnSelect?.(newCriteria)
    },
    [sortCriteria, onColumnSelect]
  )

  const handleSortRuleRemove = React.useCallback(
    (removedSort: Atoms.Table.SortModel) => {
      const newCriteria = sortCriteria.filter(
        (item) => item.id !== removedSort.id
      )
      setSortCriteria(newCriteria)
      if (newCriteria.length === 0) {
        onSortRuleClear?.()
        setSorterVisibility(SorterVisibility.NONE)
      }
      onColumnSelect?.(newCriteria)
    },
    [sortCriteria, onColumnSelect, onSortRuleClear]
  )

  return {
    sortCriteria,
    sorterVisibility,
    setSorterVisibility,
    handleSortRuleAdd,
    handleSortRuleUpdate,
    handleSortRuleRemove
  }
}

/**
 * Hook for managing column options in the sorter
 * Listens to TableSortColumnsEvent for column updates
 * @returns Array of column options for the sorter
 */
export const useSortableColumns = () => {
  const [columnOptions, setColumnOptions] = React.useState<
    Atoms.Table.MultipleColumnSorterProps['availableColumns']
  >([])

  const handleColumnListUpdate = React.useCallback((e: Event) => {
    const { columns } = (e as TableSortColumnsEvent).detail
    setColumnOptions(columns)
  }, [])

  React.useEffect(() => {
    events.addEventListener(TableSortColumnsEvent.Name, handleColumnListUpdate)

    return () => {
      events.removeEventListener(
        TableSortColumnsEvent.Name,
        handleColumnListUpdate
      )
    }
  }, [handleColumnListUpdate])

  return columnOptions
}
