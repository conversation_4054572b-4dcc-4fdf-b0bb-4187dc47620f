import React from 'react'
import { <PERSON><PERSON>, Icon, Popover, Select } from '@/atoms'

interface SortListProps {
  selectedColumns: Atoms.Table.SortModel[]
  availableColumns: App.Atoms.Select.SearchProps['options']
  isColumnSelectorVisible: boolean
  labels: {
    [key: string]: string
  }
  onSortRuleUpdate: (model: Atoms.Table.SortModel) => void
  onSortRuleAdd: (columnKey: string) => void
  onSortRuleRemove: (model: Atoms.Table.SortModel) => void
  onSortRuleAddToggle?: () => void
}

const SortConfigList = ({
  selectedColumns,
  availableColumns,
  isColumnSelectorVisible,
  labels,
  onSortRuleAddToggle,
  onSortRuleUpdate,
  onSortRuleAdd,
  onSortRuleRemove
}: SortListProps) => (
  <div className='flex flex-col items-start gap-3 min-w-[24rem]'>
    {selectedColumns.map(({ colId, sort, id }) => (
      <div key={id} className='w-full flex items-center justify-between gap-3'>
        <div className='w-6/12 border rounded-lg border-stroke'>
          <Select
            variant='borderless'
            className='w-full'
            value={colId}
            options={availableColumns}
            onChange={(value) => onSortRuleUpdate({ colId: value, sort, id })}
          />
        </div>
        <div className='w-5/12 bg-stroke px-1 flex items-center rounded-lg'>
          <Icon icon={sort === 'asc' ? 'bx:sort-up' : 'bx:sort-down'} />
          <Select
            variant='borderless'
            className='w-full'
            value={sort}
            options={[
              { label: 'Ascending', value: 'asc' },
              { label: 'Descending', value: 'desc' }
            ]}
            onChange={(value) => onSortRuleUpdate({ colId, sort: value, id })}
          />
        </div>
        <Button
          type='text'
          size='small'
          icon={<Icon icon='icon-park-outline:close' />}
          onClick={() => onSortRuleRemove({ colId, sort, id })}
        />
      </div>
    ))}
    <Button
      className='rounded-lg border-stroke'
      size='small'
      icon={<Icon icon='icon-park-outline:plus' />}
      onClick={() => onSortRuleAddToggle?.()}
    >
      {labels.addSort}
    </Button>
    {isColumnSelectorVisible && (
      <Select
        className='min-w-48'
        placeholder='Select column'
        open={isColumnSelectorVisible}
        showSearch
        options={availableColumns}
        onChange={onSortRuleAdd}
      />
    )}
  </div>
)

const MultipleColumnSorter = React.forwardRef<
  Atoms.Table.MultipleColumnSorterRef,
  Atoms.Table.MultipleColumnSorterProps
>(
  (
    {
      selectedColumns,
      availableColumns,
      onSortModelChange,
      onSortRuleAddSelector,
      onCloseSortSelector,
      ...props
    },
    ref
  ) => {
    const [isPopoverOpen, setIsPopoverOpen] = React.useState(false)
    const [isAdding, setIsAdding] = React.useState(false)

    const sortListLabels = React.useMemo(() => ({ addSort: 'Add sort' }), [])

    const handleSortRuleUpdate = React.useCallback(
      (updatedSortModel: Atoms.Table.SortModel) => {
        onSortModelChange?.(updatedSortModel)
      },
      [onSortModelChange]
    )

    const handleColumnSelectorOpen = React.useCallback(() => {
      setIsAdding(true)
    }, [])

    const handleSortRuleAdd = React.useCallback(
      (columnKey: string) => {
        if (columnKey) {
          onSortRuleAddSelector?.(columnKey)
          setIsAdding(false)
        }
      },
      [onSortRuleAddSelector]
    )

    const handleSortRuleRemove = React.useCallback(
      (sortModel: Atoms.Table.SortModel) => {
        onCloseSortSelector?.(sortModel)
      },
      [onCloseSortSelector]
    )

    React.useImperativeHandle(
      ref,
      () => ({
        setVisible: (visible: boolean) => setIsPopoverOpen(visible)
      }),
      []
    )

    return (
      <Popover
        onOpenChange={setIsPopoverOpen}
        arrow={false}
        trigger={'click'}
        open={isPopoverOpen}
        placement='bottomLeft'
        content={
          <SortConfigList
            selectedColumns={selectedColumns}
            availableColumns={availableColumns}
            isColumnSelectorVisible={isAdding}
            labels={sortListLabels}
            onSortRuleAddToggle={handleColumnSelectorOpen}
            onSortRuleUpdate={handleSortRuleUpdate}
            onSortRuleAdd={handleSortRuleAdd}
            onSortRuleRemove={handleSortRuleRemove}
          />
        }
        {...props}
      />
    )
  }
)

MultipleColumnSorter.displayName = 'MultipleColumnSorter'

export default React.memo(MultipleColumnSorter)
