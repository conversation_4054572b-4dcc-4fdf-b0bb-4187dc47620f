declare namespace Atoms.Table {
  export type Sort = 'asc' | 'desc'

  export type SortModel = { colId: string; sort: Sort; id?: string | number }

  export type MultipleColumnSorterProps = import('antd').PopoverProps & {
    selectedColumns: SortModel[]
    availableColumns: App.Atoms.Select.OptionItem[]
    onSortModelChange?: (sortModel: SortModel) => void
    onSortRuleAddSelector?: (key: string) => void
    onCloseSortSelector?: (sortModel: SortModel) => void
  }
  export type MultipleColumnSorterRef = {
    setVisible?: (visible: boolean) => void
  }
}
