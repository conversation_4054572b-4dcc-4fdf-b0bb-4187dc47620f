import React from 'react'
import { Button, Icon, Select } from '@/atoms'
import MultipleColumnSorter from './multiple-column-sorter'
import Typography from '@/atoms/typography'
import { useSortableColumns, useMultiColumnSort } from './hook'
import { useClickOutside } from '@/hooks'
import { SorterVisibility } from './enum'
import { cn } from '@/utils'

const SortToggleButton = ({
  criteria,
  onSortStateToggle,
  label
}: {
  criteria: Atoms.Table.SortModel[]
  onSortStateToggle: () => void
  label: string
}) => (
  <div
    onClick={onSortStateToggle}
    className={cn(
      'cursor-pointer border border-dashed rounded-lg border-secondary flex items-center gap-2 px-2',
      criteria.length > 0 && 'border-solid'
    )}
  >
    <Button
      size='middle'
      className='border-none p-0 h-7'
      icon={<Icon icon='iconoir:sort' />}
    >
      {label}
    </Button>
    {criteria.length > 0 && (
      <Typography.Title className='!m-0' level={3}>
        {criteria[0].colId}
      </Typography.Title>
    )}
    {criteria.length > 1 && (
      <Typography.Title className='!m-0' level={3}>
        +{criteria.length - 1}
      </Typography.Title>
    )}
  </div>
)

const TableSorter = React.forwardRef<
  Atoms.Table.TableSorterRef,
  Atoms.Table.TableSorterProps
>(({ labels, onColumnSelect, onSortRuleClear }, ref) => {
  const {
    sortCriteria,
    sorterVisibility,
    setSorterVisibility,
    handleSortRuleAdd,
    handleSortRuleUpdate,
    handleSortRuleRemove
  } = useMultiColumnSort(onColumnSelect, onSortRuleClear)
  const availableColumns = useSortableColumns()

  const handleSortModeToggle = React.useCallback(() => {
    setSorterVisibility(
      !sortCriteria.length ? SorterVisibility.SINGLE : SorterVisibility.MULTIPLE
    )
  }, [sortCriteria.length])

  useClickOutside(
    ['.ant-select-dropdown', '.ant-popover-content', '.sort-button'],
    () => setSorterVisibility(SorterVisibility.NONE)
  )

  React.useImperativeHandle(ref, () => ({}), [])

  return (
    <div className='relative flex flex-col w-max'>
      <div className='sort-button'>
        <SortToggleButton
          criteria={sortCriteria}
          onSortStateToggle={handleSortModeToggle}
          label={labels.sort}
        />
      </div>
      <div className='min-w-40 invisible absolute'>
        {sorterVisibility === SorterVisibility.SINGLE && (
          <Select.Search
            open={sorterVisibility === SorterVisibility.SINGLE}
            className='min-w-48'
            showSearch
            options={availableColumns}
            onSelect={handleSortRuleAdd}
          />
        )}
      </div>
      <div>
        {sorterVisibility === SorterVisibility.MULTIPLE && (
          <MultipleColumnSorter
            open={sorterVisibility === SorterVisibility.MULTIPLE}
            selectedColumns={sortCriteria}
            availableColumns={availableColumns}
            onSortModelChange={handleSortRuleUpdate}
            onSortRuleAddSelector={handleSortRuleAdd}
            onCloseSortSelector={handleSortRuleRemove}
          />
        )}
      </div>
    </div>
  )
})

TableSorter.displayName = 'TableSorter'

export default React.memo(TableSorter)
