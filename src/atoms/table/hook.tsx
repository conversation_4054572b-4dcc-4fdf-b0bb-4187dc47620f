import React, { useCallback, useEffect, useState } from 'react'
import {
  ColDef,
  IDatasource,
  IGetRowsParams,
  SelectionChangedEvent
} from 'ag-grid-community'
import {
  AddColumnEvent,
  CreateTableViewEvent,
  TableViewChangedEvent
} from './events'
import events from '@/events'
import { faker } from '@faker-js/faker'
import CustomHeader from './components/custom-header'
import { caseInsensitiveComparator } from './helper'
import { AgGridReact } from 'ag-grid-react'
import { Icon } from '..'
import CustomCellName from './components/custom-cell-name'
import { getCurrentSortState } from './table-sorter/helper'
import { getCurrentFilterState } from './table-filter/helper'

/**
 * Hook for managing table events and view states
 * @param views - Initial table views configuration
 * @returns Object containing view states and event handlers
 */
export const useTableViewManagement = (views: Atoms.Table.TableView[]) => {
  const [selectedRows, setSelectedRows] = useState<Atoms.Table.TableRow[]>([])
  const [selectedView, setSelectedView] =
    useState<Atoms.Table.TableView | null>(null)
  const [allViews, setAllViews] = useState<Atoms.Table.TableView[]>(views)

  const onSelectionChanged = useCallback((event: SelectionChangedEvent) => {
    setSelectedRows(event.api.getSelectedRows())
  }, [])

  const onCreateTableView = useCallback(
    (e: Event) => {
      const { name } = (e as CreateTableViewEvent).detail
      if (!allViews.length) return

      const newView: Atoms.Table.TableView = {
        id: faker.database.mongodbObjectId(),
        name,
        columns: [
          {
            field: 'name',
            headerComponent: CustomHeader,
            cellRenderer: CustomCellName,
            filter: 'agTextColumnFilter'
          }
        ]
      }

      setTimeout(() => {
        setAllViews((prev) => [...prev, newView])
      }, 300)
      setSelectedView(newView)
    },
    [allViews]
  )

  const onAddColumn = useCallback(
    (e: Event) => {
      const { column } = (e as AddColumnEvent).detail
      if (!column || !selectedView) return

      const updatedView = {
        ...selectedView,
        columns: [
          ...selectedView.columns,
          {
            field: column,
            headerComponent: CustomHeader
          }
        ]
      }
      setAllViews((prev) =>
        prev.map((view) => (view.id === updatedView.id ? updatedView : view))
      )
      setSelectedView(updatedView)
    },
    [selectedView]
  )

  const onTableViewChanged = useCallback(
    (e: Event) => {
      const { viewId } = (e as TableViewChangedEvent).detail
      const newView = allViews.find((v) => v.id === viewId)
      if (newView) setSelectedView(newView)
    },
    [allViews]
  )

  React.useEffect(() => {
    setSelectedView(views[0])
  }, [views])

  useEffect(() => {
    events.addEventListener(CreateTableViewEvent.Name, onCreateTableView)
    events.addEventListener(TableViewChangedEvent.Name, onTableViewChanged)
    events.addEventListener(AddColumnEvent.Name, onAddColumn)

    return () => {
      events.removeEventListener(CreateTableViewEvent.Name, onCreateTableView)
      events.removeEventListener(TableViewChangedEvent.Name, onTableViewChanged)
      events.removeEventListener(AddColumnEvent.Name, onAddColumn)
    }
  }, [onCreateTableView, onTableViewChanged, onAddColumn])

  return {
    selectedRows,
    selectedView,
    allViews,
    setAllViews,
    setSelectedView,
    onSelectionChanged
  }
}

/**
 * Hook for managing table column definitions
 * @param selectedView - Currently selected table view
 * @param defaultColDefProps - Default column definition properties
 * @returns Object containing column definitions and default properties
 */
export const useTableColumnDefinitions = (
  selectedView: Atoms.Table.TableView | null
) => {
  const columnDefs: ColDef[] = React.useMemo(
    () =>
      (selectedView?.columns || []).map((col) => ({
        ...col
      })),
    [selectedView]
  )

  const defaultColDef = React.useMemo(
    () => ({
      resizable: true,
      sortable: true
    }),
    []
  )

  return { columnDefs, defaultColDef }
}

/**
 * Hook for creating table data source
 * Handles both server-side and client-side data fetching
 * @param options - Data source configuration options
 * @param options.rowData - Client-side data array
 * @param options.fetchData - Server-side fetch function
 * @param options.pageSize - Number of rows per page
 * @returns AG Grid compatible data source object
 */
export const useTableDataSource = ({
  rowData,
  fetchData,
  pageSize = 50
}: Atoms.Table.UseTableDataSourceProps) => {
  return React.useMemo(() => {
    return {
      getRows: async (params: IGetRowsParams) => {
        try {
          if (fetchData) {
            const response = await fetchData({
              startRow: params.startRow,
              endRow: Math.min(params.startRow + pageSize, params.endRow),
              sortModel: params.sortModel,
              filterModel: params.filterModel
            })
            params.successCallback(response.rows, response.totalRows)
          } else {
            const start = params.startRow
            const end = Math.min(start + pageSize, params.endRow)
            const rowThisPage = rowData?.slice(start, end)
            const lastRow = rowData?.length || 0
            params.successCallback(rowThisPage || [], lastRow)
          }
        } catch {
          params.failCallback()
        }
      }
    }
  }, [rowData, fetchData, pageSize])
}

/**
 * Hook for managing AG Grid configuration and options
 * @returns {Object} Grid configuration
 * @returns {Object} gridOptions - AG Grid options with default column definitions
 * @returns {Object} tableSorterLabels - Labels for table sorter component
 */
export const useTableConfiguration = () => {
  const gridOptions = React.useMemo(
    () => ({
      defaultColDef: {
        comparator: caseInsensitiveComparator,
        unSortIcon: true,
        sortable: true,
        suppressMenu: false,
        enableSort: true
      },
      animateRows: true,
      alwaysMultiSort: true
    }),
    []
  )

  const tableSorterLabels = React.useMemo(() => ({ sort: 'Sort' }), [])

  return { gridOptions, tableSorterLabels }
}

/**
 * Hook for managing table sorting functionality
 * @param gridRef - Reference to AG Grid instance for accessing grid API
 * @param datasource - Data source instance for fetching sorted data
 * @returns {Object} Sort handlers
 * @returns {Function} handleColumnSorterSelect - Handler for applying sort models
 * @returns {Function} handleClearSort - Handler for clearing all sorts
 * @example
 * const { handleColumnSorterSelect, handleClearSort } = useTableSortManagement(gridRef, datasource)
 */
export const useTableSortManagement = (
  gridRef: React.RefObject<AgGridReact>,
  datasource: IDatasource | undefined
) => {
  const handleColumnSorterSelect = React.useCallback(
    (sortModels: Atoms.Table.SortModel[]) => {
      if (!gridRef.current?.api || !datasource) return

      const currentFilterModel = getCurrentFilterState(gridRef.current.api)

      // Apply sort state to columns
      gridRef.current.api.applyColumnState({
        state: sortModels.map((model) => ({
          colId: model.colId,
          sort: model.sort
        }))
      })

      const wrappedDataSource = {
        getRows: (params: IGetRowsParams) => {
          datasource.getRows({
            ...params,
            sortModel: sortModels,
            filterModel: currentFilterModel || params.filterModel
          })
        }
      }

      gridRef.current.api.setGridOption('datasource', wrappedDataSource)
    },
    [datasource, gridRef]
  )

  const handleClearSort = React.useCallback(() => {
    if (!gridRef.current?.api || !datasource) return

    const currentFilterModel = gridRef.current.api.getFilterModel()
    gridRef.current.api.applyColumnState({ defaultState: { sort: null } })

    const wrappedDataSource = {
      getRows: (params: IGetRowsParams) => {
        datasource.getRows({
          ...params,
          sortModel: [], 
          filterModel: currentFilterModel || params.filterModel
        })
      }
    }

    gridRef.current.api.setGridOption('datasource', wrappedDataSource)
  }, [gridRef, datasource])

  return { handleColumnSorterSelect, handleClearSort }
}
export const useTableFilterManagement = (
  gridRef: React.RefObject<AgGridReact>,
  datasource: IDatasource | undefined
) => {
  const handleFilterColumn = React.useCallback(
    (filterModel: Atoms.Table.FilterModel) => {
      if (!gridRef.current?.api || !datasource) return

      // Get current sort state using getColumnState
      const currentSortModel = getCurrentSortState(gridRef.current.api)

      // Apply filter model to grid
      gridRef.current.api.setFilterModel(filterModel)

      const wrappedDataSource = {
        getRows: (params: IGetRowsParams) => {
          datasource.getRows({
            ...params,
            filterModel,
            sortModel: currentSortModel
          })
        }
      }

      gridRef.current.api.setGridOption('datasource', wrappedDataSource)
    },
    [datasource, gridRef]
  )

  const handleClearFilter = React.useCallback(() => {
    if (!gridRef.current?.api || !datasource) return

    // Clear filter model
    gridRef.current.api.setFilterModel(null)

    // Get current sort state
    const currentSortState = gridRef.current.api.getColumnState()
    const currentSortModel = currentSortState
      .filter((state) => state.sort)
      .map((state) => ({
        colId: state.colId,
        sort: state.sort!
      }))

    const wrappedDataSource = {
      getRows: (params: IGetRowsParams) => {
        datasource.getRows({
          ...params,
          filterModel: {},
          sortModel: currentSortModel
        })
      }
    }

    gridRef.current.api.setGridOption('datasource', wrappedDataSource)
  }, [gridRef, datasource])

  return { handleFilterColumn, handleClearFilter }
}

const processObjectFields = (
  obj: unknown,
  parentPath = ''
): App.Atoms.Select.OptionItem[] => {
  if (!obj || typeof obj !== 'object' || Array.isArray(obj)) {
    return []
  }

  return Object.entries(obj).flatMap(([key, value]) => {
    const currentPath = parentPath ? `${parentPath}.${key}` : key
    const label = key.charAt(0).toUpperCase() + key.slice(1)

    if (value && typeof value === 'object' && !Array.isArray(value)) {
      const nestedFields = processObjectFields(value, currentPath)
      return [
        {
          label,
          value: currentPath,
          icon: <Icon icon='material-symbols-light:folder' />,
          extra: <Icon icon='icon-park-outline:right' />,
          items: nestedFields
        }
      ]
    }

    return [
      {
        label,
        value: currentPath,
        icon: <Icon icon='material-symbols-light:data-object' />
      }
    ]
  })
}

/**
 * Extract sortable columns from table data
 * Converts object keys to formatted column options
 * @param data - Array of table rows
 * @returns Array of column options with label and value
 * @example
 * // Returns: [{ label: 'Name', value: 'name' }, { label: 'Email', value: 'email' }]
 * extractSortableColumns([{ name: 'John', email: '<EMAIL>' }])
 */
export const extractSortableColumns = (data: Atoms.Table.TableRow[]) => {
  if (!data.length) return []
  return processObjectFields(data[0])
}
