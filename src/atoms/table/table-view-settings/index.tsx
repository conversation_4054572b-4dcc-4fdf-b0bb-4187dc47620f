import React from 'react'
import { useTableViewCustomization } from './hook'
import { Button, Icon, Select } from '@/atoms'
import { useClickOutside } from '@/hooks'

const TableViewSettings = React.forwardRef<
  Atoms.Table.TableViewSettingsRef,
  Atoms.Table.TableViewSettingsProps
>((_props, ref) => {
  const {
    customizationMenuOptions,
    isSearchVisibility,
    handleSelectedColumn,
    setIsSearchVisibility
  } = useTableViewCustomization()

  const handleOpenSearch = React.useCallback(() => {
    setIsSearchVisibility(true)
  }, [])

  React.useImperativeHandle(ref, () => ({}), [])

  useClickOutside('.table-view-setting', () => {
    setIsSearchVisibility(false)
  })

  return (
    <div className='table-view-setting relative w-full'>
      <Button
        className='shadow'
        type={'text'}
        onClick={handleOpenSearch}
        icon={<Icon icon='solar:settings-linear' />}
      >
        View settings
      </Button>
      {isSearchVisibility && (
        <div className='absolute bottom-0 w-full'>
          <Select.Search
            className='w-full invisible'
            rootClassName='table-view-setting'
            dropdownStyle={{ width: 'fit-content' }}
            open={isSearchVisibility}
            allowClear={false}
            options={customizationMenuOptions}
            onSelect={handleSelectedColumn}
          />
        </div>
      )}
    </div>
  )
})

TableViewSettings.displayName = 'TableViewSettings'

export default React.memo(TableViewSettings)
