import { Icon } from '@/atoms'
import { faker } from '@faker-js/faker'
import events, { AddColumnEvent, TableViewSettingColumns } from '@/events'
import React from 'react'

/**
 * Hook for managing table view settings and column configurations
 * Handles column selection and view customization options
 * @returns Object containing view settings options for dropdown menu
 */
export const useTableViewCustomization = () => {
  const [availableColumns, setAvailableColumns] = React.useState<
    App.Atoms.Select.OptionItem[]
  >([])

  const [isSearchVisibility, setIsSearchVisibility] = React.useState(false)

  const customizationMenuOptions = React.useMemo(
    () => [
      {
        label: faker.lorem.words(),
        value: faker.database.mongodbObjectId(),
        key: faker.database.mongodbObjectId(),
        icon: <Icon icon='iwwa:option' />,
        extra: <Icon icon='iwwa:option' />
      },
      {
        label: faker.lorem.words(),
        value: faker.database.mongodbObjectId(),
        key: faker.database.mongodbObjectId(),
        icon: <Icon icon='iwwa:option' />,
        extra: <Icon icon='iwwa:option' />
      },
      {
        label: 'Add column',
        value: 'ac',
        key: faker.database.mongodbObjectId(),
        icon: <Icon icon='weui:add-outlined' />,
        extra: <Icon icon='icon-park-outline:right' />,
        items: [
          ...availableColumns.map(({ label, value, items }) => ({
            id: faker.database.mongodbObjectId(),
            label,
            value,
            key: label,
            icon: items?.length ? (
              <Icon icon='material-symbols-light:folder' />
            ) : (
              <Icon icon='material-symbols-light:data-object' />
            ),
            extra: items?.length ? (
              <Icon icon='icon-park-outline:right' />
            ) : undefined,
            items: items?.map((subItem) => ({
              id: faker.database.mongodbObjectId(),
              label: subItem.label,
              value: `add-column_${subItem.value}`,
              key: subItem.label,
              icon: <Icon icon='material-symbols-light:data-object' />
            }))
          }))
        ],
        isLast: true
      }
    ],
    [availableColumns]
  )

  const handleSelectedColumn = React.useCallback((value: string) => {
    events.dispatchEvent(new AddColumnEvent({ column: value }))
    setIsSearchVisibility(false)
  }, [])

  const handleColumnListUpdate = React.useCallback((e: Event) => {
    const { columns } = (e as TableViewSettingColumns).detail
    setAvailableColumns(columns)
  }, [])

  React.useEffect(() => {
    events.addEventListener(
      TableViewSettingColumns.Name,
      handleColumnListUpdate
    )
    return () => {
      events.removeEventListener(
        TableViewSettingColumns.Name,
        handleColumnListUpdate
      )
    }
  }, [handleColumnListUpdate])

  return {
    customizationMenuOptions,
    isSearchVisibility,
    handleSelectedColumn,
    setIsSearchVisibility
  }
}
