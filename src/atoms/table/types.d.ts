declare namespace Atoms.Table {
  import { ColDef } from 'ag-grid-community'
  export type TableRow = {
    [key: string]:
      | string
      | number
      | boolean
      | Date
      | null
      | undefined
      | object
      | Array
  }

  export type TableView = {
    id: string
    name: string
    columns: ColDef[]
  }

  export type DataSourceParams = {
    startRow: number
    endRow: number
    sortModel?: Atoms.Table.SortModel[]
    filterModel?: Atoms.Table.FilterModel
  }

  export type DataSourceResponse = {
    rows: TableRow[]
    totalRows: number
  }

  export type UseTableDataSourceProps = {
    rowData?: TableRow[]
    fetchData?: (params: DataSourceParams) => Promise<DataSourceResponse>
    pageSize?: number
  }

  export type TableProps = import('ag-grid-react').AgGridReactProps & {
    views: TableView[]
  }
  export type TableRef = AgGridReact | null
}
