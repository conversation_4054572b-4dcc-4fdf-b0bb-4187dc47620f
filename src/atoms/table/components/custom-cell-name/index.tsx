import { Button, Icon } from '@/atoms'
import events from '@/events'
import { ShowCommentSiderEvent } from '@/pages/branches/event'
import React from 'react'
import { Link } from 'react-router'

const CustomCellName = React.forwardRef<
  App.Atoms.Table.CustomCellNameRef,
  App.Atoms.Table.CustomCellNameProps
>(({ value, data = {} }, _ref) => {
  const dispatchCommentEvent = React.useCallback(() => {
    events.dispatchEvent(new ShowCommentSiderEvent(data))
  }, [])

  return (
    <div className='group flex items-center justify-between w-full'>
      <div className='font-bold truncate flex-1 '>
        <Link
          className='rounded-md hover:text-inherit'
          to={`/branches/${data.id}`}
        >
          {value}
        </Link>
      </div>
      <div className='w-0 group-hover:w-auto opacity-0 group-hover:opacity-100 transform translate-x-2 group-hover:translate-x-0 transition-all duration-800 flex-shrink-0 ml-2'>
        <Button
          type='text'
          size='small'
          className='flex items-center justify-center text-xs'
          icon={
            <Icon icon='mdi:comment-multiple-outline' width='16' height='16' />
          }
          onClick={dispatchCommentEvent}
        >
          {data?.comments?.length}
        </Button>
      </div>
    </div>
  )
})

CustomCellName.displayName = 'CustomCellName'

export default React.memo(CustomCellName)
