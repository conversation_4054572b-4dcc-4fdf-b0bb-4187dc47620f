import Typography from '@/atoms/typography'
import React from 'react'
import PopoverSorter from './popover-sorter'
import { generatePrefixColumnIcon, generateSuffixColumnIcon } from './helper'
import { Icon } from '@iconify/react'

const CustomHeader = React.forwardRef<
  App.Atoms.Table.CustomHeaderRef,
  App.Atoms.Table.CustomHeaderProps
>((props, _ref) => {
  const { displayName, setSort, column } = props

  const handlePopoverSorterClick = React.useCallback((key: string) => {
    switch (key) {
      case 'asc':
        setSort(key)
        break
      case 'desc':
        setSort(key)
        break
      default:
        break
    }
  }, [])

  return (
    <PopoverSorter
      trigger={['click']}
      arrow={false}
      overlayClassName='w-48'
      onOptionClick={handlePopoverSorterClick}
    >
      <div className='w-full h-full text-center flex items-center justify-between cursor-pointer'>
        <div className='flex items-center justify-start'>
          {generatePrefixColumnIcon(column.getColId()) && (
            <Icon
              icon={generatePrefixColumnIcon(column.getColId())}
              width={22}
              height={22}
              className='mr-2 text-gray-500'
            />
          )}
          <Typography.Text>{displayName}</Typography.Text>
        </div>
        {generateSuffixColumnIcon(column.getColId()) && (
          <Icon
            icon={generateSuffixColumnIcon(column.getColId())}
            width={22}
            height={22}
          />
        )}
      </div>
    </PopoverSorter>
  )
})

CustomHeader.displayName = 'CustomHeader'

export default React.memo(CustomHeader)
