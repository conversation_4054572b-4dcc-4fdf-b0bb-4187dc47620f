import React from 'react'
import { Icon, Typography } from '@/atoms'
import { Popover } from 'antd'
import './style.css'

const options = [
  {
    label: 'Sort ascending',
    key: 'asc',
    icon: 'proicons:arrow-up'
  },
  {
    label: 'Sort descending',
    key: 'desc',
    icon: 'proicons:arrow-down'
  },
  {
    label: 'Move right',
    key: 'remove-right',
    icon: 'proicons:arrow-right'
  },
  {
    label: 'Edit column label',
    key: 'edit-column-label',
    icon: 'pepicons-pop:label'
  }
]

const PopoverSorter = React.forwardRef<
  App.Atoms.Table.CustomFilterRef,
  App.Atoms.Table.CustomFilterProps
>(({ children, onOptionClick, ...props }, _) => {
  const handleOptionClick = React.useCallback(
    (key: string) => onOptionClick(key),
    [onOptionClick]
  )

  return (
    <div className='custom-header-table w-full'>
      <Popover
        placement='bottomLeft'
        className='rounded-sm'
        content={
          <div>
            {options.map(({ key, icon, label }) => (
              <div
                key={key}
                className='flex items-center py-1 px-2 cursor-pointer'
                onClick={() => handleOptionClick(key)}
              >
                <div className='flex items-center gap-2 '>
                  <Icon icon={icon} />
                  <Typography.Text>{label}</Typography.Text>
                </div>
              </div>
            ))}
          </div>
        }
        {...props}
      >
        {children}
      </Popover>
    </div>
  )
})

PopoverSorter.displayName = 'PopoverSorter'

export default React.memo(PopoverSorter)
