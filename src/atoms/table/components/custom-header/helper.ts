export const generatePrefixColumnIcon = (field: string): string => {
  switch (field) {
    case 'email':
      return 'material-symbols-light:stacked-email-outline-rounded'
    case 'role':
      return 'material-symbols-light:account-child-outline-rounded'
    case 'status':
      return 'material-symbols-light:lock-open-circle-outline'
    case 'company':
      return 'icon-park-outline:building'
    default:
      return 'material-symbols-light:lock-open-circle-outline'
  }
}

export const generateSuffixColumnIcon = (field: string): string => {
  switch (field) {
    case 'email':
      return 'material-symbols-light:flash-on-outline-rounded'
    case 'role':
      return 'material-symbols-light:flash-on-outline-rounded'
    case 'status':
      return 'material-symbols-light:flash-on-outline-rounded'
    case 'company':
      return 'material-symbols-light:flash-on-outline-rounded'
    default:
      return 'material-symbols-light:flash-on-outline-rounded'
  }
}
