import React from 'react'

const CheckboxAll = React.forwardRef<
  App.Atoms.Table.CheckboxAllRef,
  App.Atoms.Table.CheckboxAllProps
>((props, _ref) => {
  const { api } = props
  const [isChecked, setIsChecked] = React.useState(false)
  const handleSelectAll = (e: React.ChangeEvent<HTMLInputElement>) => {
    const checked = e.target.checked
    setIsChecked(checked)
    if (checked) {
      api.forEachNode((node) => {
        node.setSelected(true)
      })
    } else {
      api.forEachNode((node) => {
        node.setSelected(false)
      })
    }
  }

  return (
    <div className='ag-header-cell-comp-wrapper'>
      <div
        className={`ag-wrapper ag-checkbox-input-wrapper ${
          isChecked ? 'ag-checked' : ''
        }`}
        role='presentation'
      >
        <input
          type='checkbox'
          className='ag-input-field-input ag-checkbox-input'
          onChange={handleSelectAll}
          checked={isChecked}
          id='ag-checkbox-input'
        />
        <label className='ag-checkbox-label' htmlFor='ag-checkbox-input' />
      </div>
    </div>
  )
})

CheckboxAll.displayName = 'CheckboxAll'

export default React.memo(CheckboxAll)
