:root {
  --border-color-table: rgb(238, 239, 241);
}


/* .ag-header, .ag-header-cell {
  height: 40px !important;
  min-height: 40px !important;
} */

.ag-root {
  border: none !important;
}

.ag-header {
  background-color: unset;
}

.ag-header-cell[col-id="ag-Grid-SelectionColumn"],
.ag-cell[col-id="ag-Grid-SelectionColumn"] {
  border-right: 1px solid var(--border-color-table) !important;
}

.ag-cell {
  border-right: 1px solid var(--border-color-table) !important;
  font-size: 12px;
}
/* .ag-header-row {
  height: 10px !important;
} */
.ag-header-cell {
  border-right: 1px solid var(--border-color-table) !important;
}

.ag-cell-focus:not(.ag-cell-range-selected):focus-within {
  border: 1px solid rgb(38, 109, 240) !important;
}

.ag-row {
  background-color: inherit !important;
}

.ag-cell-focus:not(.ag-cell-range-selected):focus-within {
  border: 1px solid rgb(34, 108, 227) !important;
  border-radius: 4px;
}

.ag-cell-focus:not(
    .ag-cell-range-selected
  ):focus-within[col-id="ag-Grid-SelectionColumn"] {
  border: 1px solid transparent !important;
  background-color: transparent !important;
}

.ag-center-cols-container {
  width: unset !important;
}



/* change border color table */

.ag-root-wrapper {
  border: 1px solid var(--border-color-table) !important;
  border-radius: 4px !important;
}

.ag-cell,
.ag-pinned-left-header,
.ag-header-cell {
  border-right: 1px solid var(--border-color-table) !important;  /* matching border color */
}

.ag-header {
  border-bottom: 1px solid var(--border-color-table) !important;
}

.ag-root {
  border: 1px solid var(--border-color-table) !important;
}

.ag-row {
  border-bottom: 1px solid var(--border-color-table) !important;
}

.ag-header-cell-resize::after {
  background-color: transparent !important;
  cursor: col-resize;
}

.filter-select .ant-select-selector {
  border-radius: 0px;
  border-left: 0px !important;
  border-right: 0px !important;
}
