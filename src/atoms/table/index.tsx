import React from 'react'
import { AgGridReact } from 'ag-grid-react'
import {
  AllCommunityModule,
  InfiniteRowModelModule,
  ModuleRegistry,
  TextFilterModule
} from 'ag-grid-community'
import './style.css'

ModuleRegistry.registerModules([
  AllCommunityModule,
  InfiniteRowModelModule,
  TextFilterModule
])

const CustomTable = React.forwardRef<
  Atoms.Table.TableRef,
  Atoms.Table.TableProps
>(({ ...props }, ref) => {
  return (
    <div>
      <AgGridReact
        ref={ref}
        cacheBlockSize={50}
        rowModelType='infinite'
        {...props}
      />
    </div>
  )
})

CustomTable.displayName = 'CustomTable'

export default React.memo(CustomTable)
