type CreateTableViewEventData = Atoms.Table.CreateTableViewEventData
type TableViewChangedEventData = Atoms.Table.TableViewChangedEventData

export class CreateTableViewEvent extends CustomEvent<CreateTableViewEventData> {
  static Name = 'create-table-view-event'
  constructor(data: CreateTableViewEventData) {
    super(CreateTableViewEvent.Name, { detail: data })
  }
}

export class TableViewChangedEvent extends CustomEvent<TableViewChangedEventData> {
  static Name = 'table-view-changed-event'
  constructor(data: TableViewChangedEventData) {
    super(TableViewChangedEvent.Name, { detail: data })
  }
}
