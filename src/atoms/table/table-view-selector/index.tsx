import React from 'react'
import { useTableViewDropdown } from './hook'
import { Button, Icon, Select } from '@/atoms'
import { useClickOutside } from '@/hooks'

const TableViewSelector = React.forwardRef<
  Atoms.Table.TableViewSelectorRef,
  Atoms.Table.TableViewSelectorProps
>(({ views, label }, ref) => {
  const { dropdownOptions, handleViewSelection } = useTableViewDropdown(views)

  const [isSearchVisibility, setIsSearchVisibility] = React.useState(false)

  const handleOpenSearch = React.useCallback(() => {
    setIsSearchVisibility(true)
  }, [])

  useClickOutside('.table-view-selector', () => {
    setIsSearchVisibility(false)
  })

  React.useImperativeHandle(ref, () => ({}), [])

  return (
    <div className='table-view-selector relative w-full'>
      <Button
        className='shadow'
        type={'text'}
        onClick={handleOpenSearch}
        icon={<Icon icon='material-symbols-light:grid-view' />}
      >
        {label}
      </Button>
      {isSearchVisibility && (
        <div className='absolute w-full h-full invisible bottom-0'>
          <Select.Search
            rootClassName='table-view-selector'
            dropdownStyle={{ width: 'fit-content', height: 'fit-content' }}
            open={isSearchVisibility}
            allowClear={false}
            options={dropdownOptions}
            onSelect={handleViewSelection}
          />
        </div>
      )}
    </div>
  )
})

TableViewSelector.displayName = 'TableViewSelector'

export default React.memo(TableViewSelector)
