import { Icon } from '@/atoms'
import events, { CreateTableViewEvent, TableViewChangedEvent } from '@/events'
import React from 'react'

/**
 * Hook for managing table view selection and creation
 * @param availableViews - Array of available table availableViews
 * @returns Object containing view options and selection handler
 */
export const useTableViewDropdown = (
  availableViews: Atoms.Table.TableView[]
) => {
  const dropdownOptions = React.useMemo(() => {
    return [
      ...availableViews.map((view) => ({
        label: view.name,
        value: view.id,
        icon: <Icon icon='material-symbols-light:grid-view' />,
        extra: <Icon icon='mage:dots' />
      })),
      {
        label: 'Create new view',
        value: 'create-new-view',
        icon: <Icon icon='weui:add-outlined' />,
        key: 'create-new-view',
        isLast: true
      }
    ]
  }, [availableViews])

  const handleViewSelection = React.useCallback(
    (key: string) => {
      if (key === 'create-new-view') {
        events.dispatchEvent(new CreateTableViewEvent({ name: 'View 1' }))
      } else {
        events.dispatchEvent(new TableViewChangedEvent({ viewId: key }))
      }
    },
    [availableViews]
  )

  return { dropdownOptions, handleViewSelection }
}
