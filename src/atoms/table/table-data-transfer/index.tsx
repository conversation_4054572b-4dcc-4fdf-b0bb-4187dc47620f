import React from 'react'
import { Button, Dropdown, Icon } from '@/atoms'
import events, { ShowDataTransferFormEvent } from '@/events'

export const TableDataTransfer = React.forwardRef<
  Atoms.Table.TableDataTransferRef,
  Atoms.Table.TableDataTransferProps
>(() => {
  const options = React.useMemo(() => {
    return [
      {
        label: 'Export view as CSV',
        key: 'export-view-as-csv',
        icon: <Icon icon='teenyicons:csv-outline' />
      },
      {
        label: 'Export view as Excel',
        key: 'export-view-as-excel',
        icon: <Icon icon='mdi:microsoft-excel' />
      },
      {
        label: 'Import Excel',
        key: 'import-excel',
        icon: <Icon icon='system-uicons:import' />
      }
    ]
  }, [])

  const updateInfoOptions = React.useMemo(() => {
    return [
      {
        label: 'Update Personal',
        key: 'update-personal',
        icon: <Icon icon='mdi:account-edit' />
      },
      {
        label: 'Update Work',
        key: 'update-work',
        icon: <Icon icon='mdi:briefcase-edit' />
      },
      {
        label: 'Update Documents',
        key: 'update-documents',
        icon: <Icon icon='mdi:file-document-edit' />
      },
      {
        label: 'Update Salary',
        key: 'update-salary',
        icon: <Icon icon='mdi:currency-usd' />
      }
    ]
  }, [])

  const handleOptionSelect = React.useCallback((key: string) => {
    switch (key) {
      case 'import-excel':
        events.dispatchEvent(new ShowDataTransferFormEvent({ visible: true }))
        break
    }
  }, [])

  return (
    <div className='flex items-center gap-2'>
      <div>
        <Dropdown
          onOptionSelect={handleOptionSelect}
          options={options}
          trigger={['click']}
        >
          <Button
            className='shadow'
            type={'text'}
            icon={<Icon icon='circum:import' />}
          >
            Import/Export
          </Button>
        </Dropdown>
      </div>
      <div>
        <Dropdown
          onOptionSelect={handleOptionSelect}
          placement='topLeft'
          options={updateInfoOptions}
          trigger={['click']}
        >
          <Button
            type={'primary'}
            icon={<Icon className='text-white' icon='arcticons:updater' />}
          >
            Update info
          </Button>
        </Dropdown>
      </div>
      <div>
        <Button
          type={'primary'}
          icon={
            <Icon className='text-stroke' icon='material-symbols-light:add' />
          }
        >
          New branch
        </Button>
      </div>
    </div>
  )
})

TableDataTransfer.displayName = 'TableDataTransfer'

export default React.memo(TableDataTransfer)
