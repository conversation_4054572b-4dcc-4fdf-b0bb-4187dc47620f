import React from 'react'

export const COLUMN_CLASSES = {
  name: 'w-[200px] truncate shrink-0',
  email: 'flex-1 min-w-[200px] truncate',
  role: 'w-[120px] truncate shrink-0',
  department: 'w-[150px] truncate shrink-0',
  joinDate: 'w-[120px] truncate shrink-0',
  role1: 'w-[120px] truncate shrink-0',
  role2: 'w-[120px] truncate shrink-0',
  status: 'w-[150px] truncate shrink-0',
  actions: 'w-[150px] flex items-center gap-2 justify-end shrink-0'
} as const

export const useColumn = () => {
  const columns = React.useMemo(
    () => [
      {
        key: 'name',
        title: 'Name',
        className: COLUMN_CLASSES.name
      },
      {
        key: 'email',
        title: 'Email',
        className: COLUMN_CLASSES.email
      },
      {
        key: 'role',
        title: 'Role',
        className: COLUMN_CLASSES.role
      },
      {
        key: 'department',
        title: 'Department',
        className: COLUMN_CLASSES.department
      },
      {
        key: 'joinDate',
        title: 'Join Date',
        className: COLUMN_CLASSES.joinDate
      },
      {
        key: 'role1',
        title: 'Develop',
        className: COLUMN_CLASSES.role1
      },
      {
        key: 'role2',
        title: 'Level',
        className: COLUMN_CLASSES.role2
      },
      {
        key: 'status',
        title: 'Status',
        className: COLUMN_CLASSES.status
      },
      {
        key: 'actions',
        title: '',
        hiddenTitle: true,
        className: COLUMN_CLASSES.actions
      }
    ],
    []
  )

  return columns
}
