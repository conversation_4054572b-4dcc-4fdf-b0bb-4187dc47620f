import { List } from 'antd'
import React from 'react'
import { COLUMN_CLASSES } from './use-column'
import Typography from '@/atoms/typography'
import { Tag } from '@/atoms'

const MemoizedListItem = React.memo(
  ({ item }: { item: App.Pages.Branches.Branch.Member }) => (
    <div className='py-2 rounded-md flex items-center space-x-2 sm:space-x-3 md:space-x-4 w-full'>
      <div className={COLUMN_CLASSES.name}>
        <div className='flex items-center gap-2'>
          <img
            loading='lazy'
            src={item.avatar}
            alt={item.name}
            className='w-8 h-8 rounded-full'
          />
          <Typography.Text>{item.name}</Typography.Text>
        </div>
      </div>
      <div className={COLUMN_CLASSES.email}>
        <Typography.Text>{item.email}</Typography.Text>
      </div>
      <div className={COLUMN_CLASSES.role}>
        <Typography.Text>{item.role}</Typography.Text>
      </div>
      <div className={COLUMN_CLASSES.department}>
        <Typography.Text>{item.department}</Typography.Text>
      </div>
      <div className={COLUMN_CLASSES.joinDate}>
        <Typography.Text>{item.joinDate}</Typography.Text>
      </div>
      <div className={COLUMN_CLASSES.joinDate}>
        <Typography.Text>{item.joinDate}</Typography.Text>
      </div>
      <div className={COLUMN_CLASSES.joinDate}>
        <Typography.Text>{item.joinDate}</Typography.Text>
      </div>
      <div className={COLUMN_CLASSES.status}>
        <Tag
          color={
            item.status === 'active'
              ? 'success'
              : item.status === 'inactive'
              ? 'error'
              : 'error'
          }
        >
          {item.status === 'active' ? 'success' : 'error - email not verified'}
        </Tag>
      </div>
      <div className={COLUMN_CLASSES.actions}></div>
    </div>
  )
)

export const useRenderItem = () => {
  const renderItem = (item: App.Pages.Branches.Branch.Member) => (
    <List.Item>
      <MemoizedListItem item={item} />
    </List.Item>
  )

  return renderItem
}
