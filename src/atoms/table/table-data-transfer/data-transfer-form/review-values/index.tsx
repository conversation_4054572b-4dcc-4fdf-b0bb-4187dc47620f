import { faker } from '@faker-js/faker'
import React from 'react'
import { useColumn, useRenderItem } from './hooks'
import List from '@/components/list'

type Member = App.Pages.Branches.Branch.Member

const generateMembers = (count: number = 100): Member[] =>
  Array.from({ length: count }, () => ({
    id: faker.string.uuid(),
    name: faker.person.fullName(),
    email: faker.internet.email(),
    role: faker.helpers.arrayElement(['admin', 'manager', 'member']),
    department: faker.helpers.arrayElement([
      'IT',
      'HR',
      'Marketing',
      'Sales',
      'Finance'
    ]),
    joinDate: faker.date.past().toLocaleDateString(),
    status: faker.helpers.arrayElement(['active', 'pending']),
    develop: faker.helpers.arrayElement(['Backend', 'Frontend', 'Fullstack']),
    avatar: faker.image.avatar()
  }))

export const memberList = generateMembers()

const ReviewValues = () => {
  const columns = useColumn()
  const renderItem = useRenderItem()

  return (
    <div className='h-full'>
      <List columns={columns} dataSource={memberList} renderItem={renderItem} />
    </div>
  )
}

ReviewValues.dispyName = 'ReviewValues'

export default React.memo(ReviewValues)
