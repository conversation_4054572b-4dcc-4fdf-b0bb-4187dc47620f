import Table from '@/atoms/table'
import { processInfiniteRows } from '@/atoms/table/helper'
import React from 'react'
import { useTableDataSource } from '@/atoms/table/hook'
import {
  defaultViews,
  generateFakeData
} from '@/stories/components/custom-table/CustomTable.stories'

const PreviewImport = () => {
  const fetchData = React.useCallback(
    async ({
      startRow,
      endRow,
      sortModel,
      filterModel
    }: Atoms.Table.DataSourceParams) => {
      try {
        await new Promise((resolve) => setTimeout(resolve, 1000))

        const result = processInfiniteRows(generateFakeData(50), {
          startRow,
          endRow,
          sortModel,
          filterModel
        })

        return {
          rows: result.rows,
          totalRows: result.totalRows
        }
      } catch {
        return {
          rows: [],
          totalRows: 0,
          lastRow: 0
        }
      }
    },
    []
  )

  const dataSource = useTableDataSource({
    fetchData,
    pageSize: 100
  })

  return (
    <div className='w-full h-full overflow-hidden'>
      <Table
        datasource={dataSource}
        views={defaultViews}
        className={'max-w-full w-full h-screen'}
      />
    </div>
  )
}

PreviewImport.dispyName = 'PreviewImport'

export default React.memo(PreviewImport)
