import React from 'react'
import { But<PERSON>, Icon } from '@/atoms'
import { message, Steps } from 'antd'
import UploadFile from './upload-file'
import ReviewValues from './review-values'
import PreviewImport from './preview-import'
import events, { ShowDataTransferFormEvent } from '@/events'

export const DataTransferForm = () => {
  const [current, setCurrent] = React.useState<number>(0)

  const onChange = React.useCallback((value: number) => {
    setCurrent(value)
  }, [])

  const renderStepContent = React.useCallback(() => {
    switch (current) {
      case 0:
        return <UploadFile onChange={onChange} />
      case 1:
        return <ReviewValues />
      case 2:
        return <PreviewImport />
    }
  }, [current, onChange])

  const handleClick = React.useCallback(() => {
    if (current === 1) {
      setCurrent(2)
    } else {
      message.success('Import 1000 record successful!')
      events.dispatchEvent(new ShowDataTransferFormEvent({ visible: false }))
    }
  }, [current])

  const handleCloseTransferForm = React.useCallback(() => {
    events.dispatchEvent(new ShowDataTransferFormEvent({ visible: false }))
  }, [])

  return (
    <div className='flex flex-col gap-5 m-5 mt-0 shadow-lg p-4 h-full'>
      <div className='flex items-center justify-between'>
        <Steps
          size='small'
          type='navigation'
          responsive={true}
          current={current}
          onChange={onChange}
          className='w-[30%]'
          items={[
            {
              title: 'Upload file',
              status:
                current === 0 ? 'process' : current > 0 ? 'finish' : 'wait'
            },
            {
              title: 'Preview values',
              status:
                current === 1 ? 'process' : current > 1 ? 'finish' : 'wait'
            },
            {
              title: 'Preview import',
              status:
                current === 2 ? 'process' : current > 2 ? 'finish' : 'wait'
            }
          ]}
        />
        <div>
          <Button
            onClick={handleCloseTransferForm}
            size='middle'
            shape='circle'
            icon={
              <Icon
                icon='material-symbols-light:close'
                className='text-black'
              />
            }
          ></Button>
        </div>
      </div>
      <div className='h-full overflow-auto'>{renderStepContent()}</div>
      <div className='flex items-center justify-end gap-2'>
        {current > 1 && (
          <div className='flex justify-end'>
            <Button
              onClick={handleCloseTransferForm}
              size='middle'
              type={'default'}
            >
              Cancel
            </Button>
          </div>
        )}
        {current > 0 && (
          <div className='flex justify-end'>
            <Button onClick={handleClick} size='middle' type={'primary'}>
              {current === 1 ? 'Continue' : 'Start Import'}
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}

DataTransferForm.displayName = 'DataTransferForm'

export default React.memo(DataTransferForm)
