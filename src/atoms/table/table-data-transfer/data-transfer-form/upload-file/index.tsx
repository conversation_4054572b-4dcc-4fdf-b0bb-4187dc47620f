import React from 'react'
import type { UploadProps } from 'antd'
import { Upload } from 'antd'
import { Icon } from '@/atoms'

const { <PERSON><PERSON> } = Upload

const UploadFile = React.forwardRef<
  Atoms.Table.UploadFileRef,
  Atoms.Table.UploadFileProps
>((props, ref) => {
  const { onChange } = props

  const uploadProps: UploadProps = {
    name: 'file',
    multiple: true,
    action: 'https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload',
    onChange() {
      onChange(1)
    },
    onDrop(e) {
      onChange(1)
      console.log('Dropped files', e.dataTransfer.files)
    }
  }

  React.useImperativeHandle(ref, () => ({}), [])

  return (
    <Dragger {...uploadProps}>
      <div className='ant-upload-drag-icon flex justify-center'>
        <Icon icon='line-md:uploading-loop' width='150' height='150' />
      </div>
      <p className='ant-upload-text'>
        Click or drag file to this area to upload
      </p>
      <p className='ant-upload-hint'>
        Support for a single or bulk upload. Strictly prohibited from uploading
        company data or other banned files.
      </p>
    </Dragger>
  )
})

UploadFile.displayName = 'UploadFile'

export default React.memo(UploadFile)
