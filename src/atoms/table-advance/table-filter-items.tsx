import React from 'react'
import { DatePicker, Select } from 'antd'

import Input from '../input'
import InputNumber from '../input-number'
import Slider from '../slider'
import Radio from '../radio'
import Checkbox from '../checkbox'

import type { RadioChangeEvent, SelectProps, InputRef } from 'antd'
import type { RangePickerProps } from 'antd/es/date-picker'
import { Dayjs } from 'dayjs'

type FilterItemRef<T = string> = {
  getValue(): T
}

export const TextFilter = React.memo(React.forwardRef((
  props: { label: string, className: string, value?: string, onChange?: (value: string) => void },
  ref: React.Ref<FilterItemRef>
) => {
  const { value, onChange, className, label } = props
  const inputRef = React.useRef<InputRef>(null)
  React.useImperativeHandle(ref, () => ({
    getValue: () => inputRef.current?.input?.value || ''
  }), [])

  React.useEffect(() => {
    if (inputRef.current?.input) {
      inputRef.current.input.value = value || ''
    }
  }, [value])

  const handleOnChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target

    if (onChange) onChange(value)
  }

  return (
    <div className={className}>
      <div className='flex items-center justify-end'>{label}</div>
      <Input onChange={handleOnChange} value={value} ref={inputRef} placeholder='Enter your keyword' />
    </div>
  )
}))

export const OptionFilter = React.memo(React.forwardRef((
  props: {
    className: string,
    label: string,
    items: Array<{ label: string, value: string | number }>,
    selectComponentProps?: SelectProps,
    onChange?: (value: string | number) => void,
    value: string
  },
  ref: React.Ref<FilterItemRef<string | number>>
) => {
  const { onChange, value } = props
  const inputRef = React.useRef(null)

  React.useImperativeHandle(ref, () => ({
    getValue: () => ''
  }), [])

  const handleOnChange = (value: string | number) => {
    if (onChange)  onChange(value)
  }

  return (
    <div className={props.className}>
      <div className='flex items-center justify-end'>{props.label}</div>
      <Select {...(props.selectComponentProps || {})} ref={inputRef} onChange={handleOnChange} value={value}>
        {props.items.map((item, index) => (
          <Select.Option value={item.value} selected={index === 0} key={item.value}>{item.label}</Select.Option>
        ))}
      </Select>
    </div>
  )
}))

export const NumberFilter = React.memo(React.forwardRef((
  props: {
    className: string,
    label: string,
    value?: number,
    min: number,
    max: number,
    step?: number,
    onChange?: (value: number | null) => void
  },
  ref: React.Ref<FilterItemRef<number | undefined>>
) => {
  const { className, label, min, max, step, onChange, value } = props
  const inputRef = React.useRef<HTMLInputElement>(null)

  React.useImperativeHandle(ref, () => ({
    getValue: () => inputRef.current?.value ? parseInt(inputRef.current?.value) : undefined
  }), [])

  const handleOnChange = (value: number | null) => {
    if (onChange) onChange(value)
  }

  return (
    <div className={className}>
      <div className='flex items-center justify-end'>{label}</div>
      <InputNumber ref={inputRef} min={min} max={max} step={step} value={value} placeholder={min.toString()} className='w-full' onChange={handleOnChange}/>
    </div>
  )
}))

export const RangeFilter = React.memo(React.forwardRef((
  props: {
    type?: string,
    className: string,
    label: string,
    min: number,
    max: number,
    value?: number | number[] | undefined,
    onChange?:(value: number | number[] | undefined) => void
  },
  ref: React.Ref<FilterItemRef<{ from: number, to: number }>>
) => {
  const { type, className, label, min, max, value, onChange } = props
  const inputRef = React.useRef(null)

  const isSlider = type === 'rangeSlider'

  React.useImperativeHandle(ref, () => ({
    getValue: () => ({ from: 0, to: 0 })
  }), [])

  const handleOnChange = (value: number | number[] | undefined) => {
    if (onChange) onChange(value)
  }

  return (
    <div className={className}>
      <div className='flex items-center justify-end'>{label}</div>
      {isSlider ? (
      <Slider range ref={inputRef} min={min} max={max} value={value as number[] | undefined} onChange={handleOnChange}/>
      ) : (
        <Slider ref={inputRef} min={min} max={max} value={value as number | undefined} onChange={handleOnChange}/>
      )}
    </div>
  )
}))

export const CheckboxFilter = React.memo(React.forwardRef((
  props: {
    className: string,
    label: string,
    items: Array<{ label: string, value: number | string }>,
    type?: string,
    onChange?: (event: RadioChangeEvent | string[] ) => void,
    value: string[],
    render?: (
      options: Array<{ label: string, value: number | string }>,
      label: string, value: string[],
      onChange?: ( values: string[] ) => void
    ) => string | number | boolean | React.ReactNode | JSX.Element | undefined | null
  },
  ref: React.Ref<FilterItemRef<string | number>>
) => {
  const { className, label, items, type, onChange, value, render } = props
  const inputRef = React.useRef(null)

  React.useImperativeHandle(ref, () => ({
    getValue: () => ''
  }), [])

  const handleOnChange = (event: RadioChangeEvent | string[] ): void =>  {    
    let value = event
    if (type === 'radio' && 'target' in event) {
      value = (event as RadioChangeEvent).target.value
    }
    if (onChange) onChange(value)
   }

  return (
    render  ? (render(items, label, value, onChange)) : (
      <div className={className}>
        <div className='flex items-center justify-end'>{label}</div>
        {type === 'radio' ? (
          <Radio.Group ref={inputRef} onChange={handleOnChange} value={value}>
            {items.map(item => (
              <Radio key={item.value} value={item.value}>{item.label}</Radio>
            ))}
          </Radio.Group>
        ) : (type === 'checkbox' && (
          <Checkbox.Group ref={inputRef} onChange={handleOnChange} value={value}>
            {items.map(item => (
              <Checkbox key={item.value} value={item.value}>{item.label}</Checkbox>
            ))}
          </Checkbox.Group>
        ))}
      </div>
    )
  )
}))


export const DateFilter = React.memo(React.forwardRef((
  props: {
    type?: string
    className: string
    label: string
    onChange?: (dateObj: [ Dayjs | Atoms.TableAdvance.NoUndefinedRangeValueType<Dayjs> | [Dayjs, Dayjs], string | string[] | [string, string]]) => void,
    value?: Dayjs | undefined 
  },
  ref: React.Ref<FilterItemRef<Date | undefined>>
) => {
  const { type, className, label, onChange, value } = props

  React.useImperativeHandle(ref, () => ({
    getValue: () => undefined
  }), [])

  const handleOnChange = (date: Dayjs | Atoms.TableAdvance.NoUndefinedRangeValueType<Dayjs> | [Dayjs, Dayjs], dateString: string | string[] | [string, string]): void => {
    if (onChange) onChange([date, dateString])
  }

  const valueCurrent = Array.isArray(value) ? value[0] : (Dayjs || [Dayjs, Dayjs])
  return (
    <div className={className}>
      <div className='flex items-center justify-end'>{label}</div>
      {type === 'date' ? (
        <DatePicker onChange={handleOnChange} value={valueCurrent} />
      ) : (type === 'rangeDate') && (<DatePicker.RangePicker onChange={handleOnChange as RangePickerProps['onChange']} value={valueCurrent}/>
      )}
    </div>
  )
}))

