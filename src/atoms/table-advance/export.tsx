import React from 'react'
import { theme } from 'antd'
import Button from '../button'
import Popover from '../popover'
import Typography from '../typography'
import { Icon } from '@iconify/react'

const ExportComponent = (_props: {}, ref: React.Ref<{}>) => {
  const { token } = theme.useToken()
  
  React.useImperativeHandle(ref, () => ({

  }), [])

  const iconStyle = React.useMemo<React.CSSProperties>(() => ({
    color: token.colorTextSecondary,
    fontSize: 18
  }), [token])

  return (
    <Popover
      trigger={'click'}
      content={(
        <div className='p-2 min-w-[100px] flex flex-col gap-3'>
          <div className='flex gap-2 items-center cursor-pointer'>
            <Icon icon="bi:filetype-txt" style={iconStyle} />
            <Typography.Text>Export as Text (.txt)</Typography.Text>
          </div>
          <div className='flex gap-2 items-center cursor-pointer'>
            <Icon icon="vscode-icons:file-type-excel" style={iconStyle}/>
            <Typography.Text>Export as Excel (xlsx)</Typography.Text>
          </div>
          <div className='flex gap-2 items-center cursor-pointer'>
            <Icon icon="material-symbols:inbox-customize-outline" style={iconStyle}/>
            <Typography.Text>Custom export</Typography.Text>
          </div>
        </div>
      )}
    >
      <Button className='flex items-center gap-1 px-2'>
        <Icon icon="pajamas:export" style={iconStyle} />
        <Typography.Text>Export</Typography.Text>
        <Icon icon="solar:alt-arrow-down-line-duotone" style={iconStyle}/>
      </Button>
    </Popover>
  )
}

export const Export = React.memo(React.forwardRef(ExportComponent))
