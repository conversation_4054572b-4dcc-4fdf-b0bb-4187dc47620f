import React from 'react'
import { Icon } from '@iconify/react'
import { theme } from 'antd'
import type { SortDirection, ColumnEvent } from 'ag-grid-community';

import { cn } from '@/utils'

function doNothing() {}

const CustomHeader = (props: App.Atoms.TableAdvance.HeaderProps) => {
  const { icon, setSort, headerNameClass, containerClass, column } = props
  const { token } = theme.useToken()
  const [sort, setSortState] = React.useState<SortDirection | null>(column.getSort() || null)
  const eventRef = React.useRef<React.MouseEvent<HTMLDivElement> | null>(null)

  const textStyle = React.useMemo<React.CSSProperties>(() => ({
    color: token.colorTextHeading
  }), [token])

  const onHeaderClick = React.useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    eventRef.current = e
    setSortState(oldValue => {
      switch (oldValue) {
        case 'asc': return 'desc'
        case 'desc': return null
        case null: return 'asc'
        default: return null
      }
    })
  }, [column])

  React.useEffect(() => {
    if (eventRef.current) {
      setSort(sort, eventRef.current.shiftKey)
    }
  }, [sort, setSort])

  React.useEffect(() => {
    const listener = (e: ColumnEvent<'sortChanged'>) => {
      console.log(e.api.getState().sort)
    };

    column.addEventListener('sortChanged', listener);
    return () => {
      column.removeEventListener('sortChanged', listener);
    }
  }, [column])

  let iconElement
  if (icon && typeof icon === 'string') {
    iconElement = <Icon icon={icon} style={textStyle}/>
  } else if (icon) {
    iconElement = icon
  }

  let sortIcon
  const sortIconStyle = 'text-green-500 text-[16px]'
  if (sort === 'asc') {
    sortIcon = <Icon icon="gravity-ui:bars-descending-align-left-arrow-down" className={sortIconStyle} />
  } else if (sort === 'desc') {
    sortIcon = <Icon icon="gravity-ui:bars-ascending-align-left-arrow-up" className={sortIconStyle} />
  }

  const sortable = column.isSortable();
  return (
    <div
      className={cn('w-full h-full flex gap-1 items-center relative', sortable && 'cursor-pointer', containerClass)}
      onClick={sortable ? onHeaderClick : doNothing}
    >
      {iconElement && <div className='flex items-center' style={textStyle}>{iconElement}</div>}
      <div className={cn('truncate grow', headerNameClass)} style={textStyle}>{props.displayName}</div>
      {sortIcon && <div className='absolute right-0 top-[10px]'>{sortIcon}</div>}
    </div>
  )
}

export const Header = React.memo<App.Atoms.TableAdvance.HeaderProps>(CustomHeader)
