import React from "react";
import { AgGridReact } from "ag-grid-react";
import {
  GridReadyEvent,
  GridPreDestroyedEvent,
  ColumnState,
  CellMouseOverEvent,
  CellMouseOutEvent,
  SelectionChangedEvent,
  GetRowIdFunc
} from "ag-grid-community";
import { DatePicker, theme, Skeleton } from "antd";
import { debounce, isFunction, get } from "lodash";

import Button from "../button";
import Input from "../input";
import { Icon } from "@iconify/react";
import type { RangePickerProps } from "antd/es/date-picker";
import { Dayjs } from "dayjs";

import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-quartz.css";

import { useCustomStyle, useCustomDataSource } from "./hooks";
import { Header } from "./header";
import { Filter } from "./table-filter";
import { TableSetting } from "./table-setting";
import { Export } from "./export";
import { StorageService } from "@/services";

import SelectionBar from "./selection-bar";
import RowActions from "./row-actions";
import { useCellSelection } from "./cell-selection";

const storageService = new StorageService();

import "./styles.css";
import {
  provideGlobalGridOptions
} from "ag-grid-community";
import { getColumnDisplayName } from "./helpers";

import { cn } from "@/utils"
provideGlobalGridOptions({
  theme: "legacy",
  rowClass: "border-b",
  suppressColumnVirtualisation: true
});

const CustomLoadingOverlay = () => (
  <div className="flex gap-2">
    <Skeleton.Button />
    <Skeleton.Button />
    <Skeleton.Input />
  </div>
);

const getRowElement = (cellElement: HTMLElement): HTMLElement | null => {
  return cellElement.closest(".ag-row");
};

const CustomNoRows = () => (
  <div className="ag-overlay-loading-center" role="presentation">
    😕 Không có dữ liệu để hiển thị.
  </div>
);

const shouldHideConfig = (
  relatedTarget: EventTarget | null,
  configRef: App.Atoms.TableAdvance.RowActionsRef
): boolean => {
  if (!relatedTarget || !(relatedTarget instanceof HTMLElement)) return false;

  return (
    relatedTarget !== configRef.getConfigRef() ||
    relatedTarget.hasAttribute("row-index") ||
    relatedTarget.classList.contains("ag-header")
  );
};

const findResultRowElement = (rowIndex: string | null): HTMLElement | null => {
  if (!rowIndex) return null;
  const rowElements = document.querySelectorAll(`[row-index="${rowIndex}"]`);
  return rowElements.length === 2
    ? (rowElements[1] as HTMLElement)
    : (rowElements[0] as HTMLElement);
};

const TableComponent = <TData extends object>(
  props: App.Atoms.TableAdvance.TableProps<TData>,
  ref: React.Ref<App.Atoms.TableAdvance.TableRef<TData>>
) => {
  const {
    containerClassName,
    defaultFilterData,
    fetchData,
    onGridReady,
    onSelectionChanged,
    columnDefs,
    components,
    filterConfigs,
    rowData,
    tableId,
    rowAction,
    selectionBarActions,
    loadingOverlayComponent = CustomLoadingOverlay,
    type = 'table',
    hideTableTools = false,
    hideListBorder = false,
    suspendCellSelection = true,
    cellSelectionOptions,
    ...restProps
  } = props;
  const { token } = theme.useToken();
  const [filter, setFilter] = React.useState<App.Atoms.TableAdvance.FilterData<TData>>({});
  const [keyword, setKeyword] = React.useState<string | undefined>();
  const [date, setDate] =React.useState<App.Atoms.TableAdvance.DateFilter>();
  const [columnState, setColumnState] = React.useState<App.Atoms.TableAdvance.CustomColumnState[]>([]);
  const container = React.useRef<HTMLDivElement>(null);

  const rowActionsRef = React.useRef<App.Atoms.TableAdvance.RowActionsRef>(null);
  const selectionBarRef = React.useRef<App.Atoms.TableAdvance.SelectionBarRef>(null);

  const gridRef = React.useRef<AgGridReact<TData>>(null);

  const customStyle = useCustomStyle(type);
  const dataSource = useCustomDataSource(fetchData, keyword, filter, date, gridRef);

  const { register: registerCellSelection, actionsPlaceholder } = useCellSelection<TData>(container, cellSelectionOptions);

  const onGridReadyHandler = React.useCallback(
    (params: GridReadyEvent<TData>) => {
      const columnStateLocal = storageService.local.load(
        tableId
      ) as unknown as ColumnState[];

      if (columnStateLocal) {
        params.api.applyColumnState({
          state: columnStateLocal
        });
      }

      const customColumnState = (
        columnStateLocal || params.api.getColumnState()
      ).map((item) => {
        const displayName = getColumnDisplayName(params.api, item.colId);

        return {
          ...item,
          displayName: displayName
        } as App.Atoms.TableAdvance.CustomColumnState;
      });

      setColumnState(customColumnState);

      !suspendCellSelection && registerCellSelection(params);

      if (!rowData) {
        params.api.setGridOption("datasource", dataSource);
        params.api.sizeColumnsToFit();
      }

      if (onGridReady) {
        onGridReady(params);
      }
    },
    [onGridReady, dataSource, rowData, registerCellSelection, suspendCellSelection]
  );

  const customComponents = React.useMemo<{
    [p: string]: unknown;
  }>(
    () => ({
      agColumnHeader: Header,
      // customNoRowsOverlay: CustomNoRows,
      noRowsOverlayComponent: CustomNoRows,
      ...(components || {})
    }),
    [components]
  );

  const handleColumnDisplay = React.useCallback(
    (info: { colId: string; hide: boolean }) => {
      gridRef.current?.api?.applyColumnState({
        state: [info]
      });
    },
    []
  );

  const handleFilterOnChange = React.useCallback(
    (newFilter: App.Atoms.TableAdvance.FilterData<TData> | undefined) => {

      if (filter) setFilter({ ...filter, ...newFilter });
    },
    []
  );

  const handleInputSearchOnPressEnter = React.useCallback(
    (event: React.KeyboardEvent<HTMLInputElement>) => {
      const { value } = event.target as HTMLInputElement;
      setKeyword(value);

      gridRef.current!.api.setGridOption("quickFilterText", value);
    },
    []
  );

  const handleInputSearchOnSearch = React.useCallback((value: string) => {
    setKeyword(value);

    gridRef.current!.api.setGridOption("quickFilterText", value);
  }, []);

  const debounceSearch = React.useCallback(
    debounce((value: string) => {
      setKeyword(value);
      gridRef.current!.api.setGridOption("quickFilterText", value);
    }, 300),
    []
  );

  const handleInputSearchOnChange = React.useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const { value } = e.target as HTMLInputElement;

      debounceSearch(value);
    },
    [debounceSearch]
  );

  const onTimeRangeChange = React.useCallback(
    (
      dateObj:
        | Dayjs
        | App.Atoms.TableAdvance.NoUndefinedRangeValueType<Dayjs>
        | [Dayjs, Dayjs],
      dateString: string | string[] | [string, string]
    ): void => {
      setDate([dateObj, dateString]);
    },
    []
  );

  const handleGridPreDestroyed = React.useCallback((event: GridPreDestroyedEvent) => {
    const { api } = event;
    const columnState = api.getColumnState();

    storageService.local.save(tableId, columnState);
  }, [tableId])

  const currenRowIndexRef = React.useRef<string | null>(null);
  const getCurrentRowData = React.useCallback(() => {
    if (!currenRowIndexRef.current) return null;

    return gridRef.current?.api.getDisplayedRowAtIndex(Number(currenRowIndexRef.current))?.data ?? null;
  }, [])

  const updateConfigPosition = React.useCallback((_rowElement: HTMLElement) => {
    const configElement = rowActionsRef.current?.getConfigRef();
    const configParentRect = configElement?.parentElement?.getBoundingClientRect();
    if (!configParentRect) return;

    const configWidth = configElement?.offsetWidth ?? 0;
    const calculatedX = configParentRect.left + configParentRect.width - configWidth - 20;

    const configRect = configElement?.getBoundingClientRect();
    if (!configRect) return;

    const gridContainer = configElement?.closest('.tb-type-table');
    if (!gridContainer) return;

    const currentRowIndex = Number(currenRowIndexRef.current);
    const targetRowRect = gridContainer
      .querySelector(`[row-index="${currentRowIndex}"]`)
      ?.getBoundingClientRect();
    
    const calculatedY = targetRowRect?.top;
    if (calculatedY == null) return;

    rowActionsRef.current?.setPosition({
      x: calculatedX,
      y: calculatedY
    });
  }, []);


  const gridOptions = React.useMemo(
    () => ({
      onCellMouseOver: (event: CellMouseOverEvent<TData>) => {
        rowActionsRef.current?.show();
        if (!(event.event instanceof MouseEvent)) return;

        const cellElement = event.event.target as HTMLElement;
        const rowElement = getRowElement(cellElement);
        if (!rowElement) return;

        const rowIndex = rowElement.getAttribute("row-index");
        currenRowIndexRef.current = rowIndex;

        const resultRowElement = findResultRowElement(rowIndex);
        if (!resultRowElement) return;

        updateConfigPosition(resultRowElement);
      },
      onCellMouseOut: (event: CellMouseOutEvent<TData>) => {
        if (!(event.event instanceof MouseEvent)) return;

        if (
          rowActionsRef.current &&
          shouldHideConfig(event.event.relatedTarget, rowActionsRef.current)
        ) {
          rowActionsRef.current?.hide();
          currenRowIndexRef.current = null;
        }
      },
      getRowId: ((params) => get(params.data, '_id')) as GetRowIdFunc<TData>,
      // noRowsOverlayComponent: CustomNoRows,
      ...restProps.gridOptions
    }),
    [restProps.gridOptions, updateConfigPosition]
  );

  const [selectedRows, setSelectedRows] = React.useState<TData[]>([]);
  const handleSelectionChanged = React.useCallback(
    (event: SelectionChangedEvent<TData, any>) => {
      const currentData = event.api.getSelectedRows();
      setSelectedRows(currentData);
      onSelectionChanged?.(currentData);
      if (currentData.length > 0) {
        selectionBarRef.current?.show();
      } else {
        selectionBarRef.current?.hide();
      }
    },
    []
  );

  const refresh = React.useCallback(() => {
    gridRef.current?.api.refreshInfiniteCache();
  }, []);

  React.useEffect(() => {
    dataSource && setTimeout(() => gridRef.current?.api.setGridOption('datasource', dataSource));
  }, [dataSource])

  React.useImperativeHandle(
    ref,
    () => ({
      refresh: () => setTimeout(() => gridRef.current?.api?.refreshInfiniteCache()),
      focus() {},
      gridApi: () => gridRef.current?.api,
      setFilter: (f) => {
        if (isFunction(f)) {
          setFilter(filter => f(filter));
        } else {
          setFilter(f || {});
        }
      },
    }),
    []
  );

  const className = cn(
    "ag-theme-quartz ",
    "relative",
    `tb-type-${type}`,
    type === 'list' && !hideListBorder ? 'list-border' : '',
    containerClassName
  );

  return (
    <div
      className={className}
      style={customStyle}
      ref={container}
    >
      {!!rowAction 
      && (
        <RowActions<TData>
          ref={rowActionsRef}
          getRowHoverData={getCurrentRowData}
          configs={rowAction.configs ?? []}
          className={rowAction.className ?? ''} 
          tableRef={gridRef}
        />
      )}
      {!hideTableTools && (
        <div className="my-2 flex items-center justify-between">
          <div className="flex gap-2 items-center">
            <Filter<TData>
              configs={filterConfigs || {}}
              onChange={handleFilterOnChange}
              defaultData={defaultFilterData}
            />
            <DatePicker.RangePicker
              onChange={onTimeRangeChange as RangePickerProps["onChange"]}
              className="min-w-[260px]"
            />
            <Input.Search
              placeholder="Tìm kiếm dữ liệu"
              onPressEnter={handleInputSearchOnPressEnter}
              onSearch={handleInputSearchOnSearch}
              onChange={handleInputSearchOnChange}
            />
          </div>

          <div className="flex gap-2 items-center">
            <Export />
            <Button className="flex items-center px-2">
              <Icon
                icon="zondicons:refresh"
                fontSize={18}
                style={{ color: token.colorTextSecondary }}
                onClick={refresh}
              />
            </Button>
            <TableSetting
              columnState={columnState}
              onColumnDisplayChange={handleColumnDisplay}
            />
          </div>
        </div>
      )}

      {/* Các actions khi chọn nhiều cell */}
      {actionsPlaceholder}

      {/* Main table */}
      <AgGridReact<TData>
        ref={gridRef}
        rowSelection={"multiple"}
        rowModelType={rowData ? "clientSide" : "infinite"}
        // rowData={rowData}
        {...(!rowData
          ? {
              cacheBlockSize: restProps.cacheBlockSize || 100,
              cacheOverflowSize: 1,
              maxConcurrentDatasourceRequests: 1,
              infiniteInitialRowCount: 1,
              maxBlocksInCache: 2
            }
          : {
            rowData: rowData
          })}
        onGridReady={onGridReadyHandler}
        components={customComponents}
        columnDefs={columnDefs}
        onGridPreDestroyed={handleGridPreDestroyed}
        onSelectionChanged={handleSelectionChanged}
        gridOptions={gridOptions}
        // loadingOverlayComponent={loadingOverlayComponent}
        // loadingCellRenderer={<Skeleton.Input />} // The grid will rerender caused by Skeleton.Input
        noRowsOverlayComponent={CustomNoRows}
        {...restProps}
      />
      {selectionBarActions && (
        <SelectionBar
          ref={selectionBarRef}
          rowCount={selectedRows.length}
          rowData={selectedRows ?? []}
          actions={selectionBarActions}
          className="absolute bottom-0 left-1/2 -translate-x-1/2"
        />
      )}
    </div>
  );
};

export default React.memo(React.forwardRef(TableComponent)) as <
  TData extends object
>(
  props: React.PropsWithoutRef<App.Atoms.TableAdvance.TableProps<TData>> &
    React.RefAttributes<App.Atoms.TableAdvance.TableRef<TData>>
) => ReturnType<typeof TableComponent>;
