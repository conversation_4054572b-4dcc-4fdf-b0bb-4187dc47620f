import React from 'react'
import { theme } from 'antd'
import { IDatasource, IGetRowsParams } from 'ag-grid-community'
import { AgGridReact } from 'ag-grid-react';
import type dayjs from 'dayjs';

export const useCustomStyle = (type: 'table' | 'list'): React.CSSProperties => {
  const { token } = theme.useToken()
  const customStyle: React.CSSProperties = React.useMemo(() => ({
    width: '100%',
    height: '98%',
    ...(type === 'table' && {
      '--ag-checkbox-checked-color': token.colorPrimary,
      '--ag-input-focus-box-shadow': `${token.colorPrimary}cc 0px 0px 0px 0px`,
    })
  }), [token])

  return customStyle
}

export const useCustomDataSource = <TData extends object>(
  fetchData?: App.Atoms.TableAdvance.FetchDataFn<TData>,
  keyword?: string,
  filter?: { [key in keyof TData]?: unknown },
  date?: App.Atoms.TableAdvance.DateFilter,
  gridRef?: React.RefObject<AgGridReact<TData>>,
) => {
  if (!fetchData) return undefined

  const dataSource = React.useMemo<IDatasource>(() => {
    return {
      rowCount: undefined,
      getRows: (params: IGetRowsParams) => {
        const fetchFilter: App.Atoms.TableAdvance.TableFilter<TData> = {
          keyword,
          start: params.startRow,
          end: params.endRow,
          filter: Object.keys(params.filterModel).length ? params.filterModel : filter
        };

        const filterByCreatedDate = date ? date[0] as unknown as [dayjs.Dayjs, dayjs.Dayjs] : [];

        if (filterByCreatedDate?.length) {
          fetchFilter.createdBefore = filterByCreatedDate[0].format('MM/DD/YYYY');
        }
        if (filterByCreatedDate?.length) {
          fetchFilter.createdAfter = filterByCreatedDate[1].format('MM/DD/YYYY');
        }

        if (params.sortModel?.length && gridRef?.current) {
          const grid = gridRef?.current;

          if (!fetchFilter.sort) {
            fetchFilter.sort = {};
          };

          // console.log(params.sortModel, grid.api.getState())
          params.sortModel.forEach(s => {
            const col = grid.api.getColumnDef(s.colId);
            if (col?.field) {
              const key = col.field;
              // @ts-ignore
              fetchFilter.sort[key] = s.sort;
            }
          })
          // get column by sort.colId >> colDef >> fieldName

        }

        // console.log('asking for ' + params.startRow + ' to ' + params.endRow)
        fetchData(fetchFilter).then((result) => {
          // if on or after the last page, work out the last row.
          if (result.total === 0) {
            gridRef?.current?.api.showNoRowsOverlay()
            // gridRef?.current?.api.setRowCount(-1)
            // return
          }

          let lastRow = -1;
          if (result.end === result.total) {
            lastRow = result.end
          }
          // call the success callback
          params.successCallback(result.items, lastRow)
        })
      }
    }
  }, [filter, keyword, fetchData, date, gridRef])

  return dataSource
}