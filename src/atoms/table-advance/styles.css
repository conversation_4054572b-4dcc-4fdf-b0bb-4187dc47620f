.ag-theme-quartz {
  --ag-header-height: 36px;
  --ag-row-height: 36px;
  --ag-header-column-separator-display: none;
  --ag-header-column-separator-color: transparent !important;
}

.ag-root-wrapper {
  border: none !important;
}

.ant-tree-treenode {
  @apply flex;
}

.ag-theme-quartz.tb-type-table {
  /* Selection */
  --ag-range-selection-border-color: rgb(193, 0, 97);
  --ag-range-selection-border-style: dashed;
  --ag-range-selection-background-color: rgb(255, 0, 128, 0.1);

  /* Header */
  --ag-header-column-separator-display: block;
  --ag-header-column-separator-height: 100%;
  --ag-header-column-separator-width: 1px;

  --ag-header-column-resize-handle-display: block;
  --ag-header-column-resize-handle-height: 100%;
  --ag-header-column-resize-handle-width: 5px;
  --ag-header-column-resize-handle-color: transparent;

  /* Cell */
  --ag-cell-horizontal-border: 1px solid var(--ag-row-border-color);
  --ag-cell-horizontal-padding: 12px;

  /* these next 3 variables control the background colour when 2, 3 or 4+ ranges overlap,
     and should have progressively greater opacity to look realistic - see the docs below
     for the correct formulas to use */
  --ag-range-selection-background-color-2: rgb(255, 0, 128, 0.19);
  --ag-range-selection-background-color-3: rgb(255, 0, 128, 0.27);
  --ag-range-selection-background-color-4: rgb(255, 0, 128, 0.34);

  --ag-range-selection-highlight-color: rgb(60, 188, 0, 0.3);
}

/* .ag-theme-quartz .ag-cell {
  border-top-color: var(--ag-row-border-color);
  border-bottom-color: 1px solid var(--border-color-table) !important;
} */

.ag-theme-quartz.tb-type-list {
  --ag-cell-horizontal-border: 0px !important;
  --ag-cell-horizontal-border: 0px !important;
  --ag-selected-row-background-color: transparent !important;
  --ag-hover-row-background-color: transparent !important;
  --ag-row-numbers-selected-color: transparent !important;
  --border-color-table: transparent !important;
  --ag-active-color: transparent !important;
  --ag-header-background-color: transparent !important;
}

.ag-theme-quartz.tb-type-list .ag-cell {
  border-top-color: transparent !important;
  border-bottom-color: transparent !important;
}

.ag-theme-quartz.tb-type-list .ag-row,
.ag-theme-quartz.tb-type-list .ag-root-wrapper {
  border: transparent !important;
}

.tb-type-list .ag-row.ag-row-hover,
.tb-type-list .ag-row.ag-row-hover:before,
.tb-type-list .ag-row.ag-row-no-focus,
.tb-type-list .ag-row.ag-row-selected,
.tb-type-list .ag-cell.ag-cell-focus {
  background-color: transparent !important;
  cursor: default;
  border-left: 1px solid transparent !important;
}

.tb-type-list.list-border .ag-row {
  border-bottom: 1px solid var(--ag-row-border-color) !important;
}