import React from 'react'
import Button from '../button'
import Popover from '../popover'
import Typography from '../typography'
import { Icon } from '@iconify/react'
import { Dayjs } from 'dayjs'
import { isEqual } from 'lodash'

import { TextFilter, RangeFilter, OptionFilter, NumberFilter, CheckboxFilter, DateFilter } from './table-filter-items'
// import _ from 'lodash'

const TableFilterComponent = <TData extends object>(
  props: App.Atoms.TableAdvance.TableFilterProps<TData>,
  ref: React.Ref<App.Atoms.TableAdvance.TableFilterRef<TData>>
) => {
  const { configs, onChange, defaultData } = props
  const [filter, setFilter] = React.useState<App.Atoms.TableAdvance.FilterData<TData>>({})
  const [open, setOpen] = React.useState(false)

  React.useImperativeHandle(ref, () => ({
    show: () => setOpen(true),
    hide: () => setOpen(false),
    getFilter: () => filter
  }), [filter])

  React.useEffect(() => {
    setFilter(old => {
      if (isEqual(defaultData, old)) {
        return old
      }
      return defaultData ?? {}
    })
  }, [defaultData])

  const filterCount = Object.keys(filter).length

  const handleApplyFilter = React.useCallback((_e: React.MouseEvent<HTMLDivElement>) => {
    if (onChange) {
      onChange(filter)
    }
    setOpen(false)
  }, [filter, onChange])

  const handleClearAll = React.useCallback((_e: React.MouseEvent<HTMLDivElement>) => {
    // gridRef.current!.api.setFilterModel(null);
    // gridRef.current!.api.setGridOption("rowData", getRowData());

    console.log(filter);
    setFilter({})
    console.log('Delete');
  }, [filter])

  const handleFilterChange = (configKey: keyof TData, value: unknown) => {
    if (!configKey && !value) return

    const newFilter = { [configKey as keyof TData]: value }
    setFilter({ ...filter, ...newFilter })
  }

  const renderFilterComponent = (config: App.Atoms.TableAdvance.TableFilterConfigItem, configKey: keyof TData) => {
    if (!config.type && !config.label && !configKey) return

    const itemClass = 'grid grid-cols-[80px_minmax(100px,_1fr)] gap-2 place-self-center'
    const { type, label, render } = config
    
    if (type === 'text') {
      return <TextFilter 
              label={label} 
              className={itemClass} 
              value={filter[configKey] as string} 
              onChange={(value) => handleFilterChange(configKey, value)} 
              />
    }

    if (type === 'date' || type === 'rangeDate') {
      return <DateFilter 
              type={type} 
              label={label} 
              className={itemClass} 
              value={filter[configKey] as Dayjs} 
              onChange={(date) => handleFilterChange(configKey, date)} 
              />
    }

    switch (type.type) {
      case 'number': return (
        <NumberFilter 
          label={label} 
          className={itemClass} 
          min={type.min} 
          max={type.max} 
          step={type.step ?? 1} 
          onChange={(value) => handleFilterChange(configKey, value)} 
          value={filter[configKey] as number} 
          />
      )
      case 'checkbox':
      case 'radio':
        return (
          <CheckboxFilter 
            className={itemClass} 
            type={type.type}
            label={label} 
            items={type.options} 
            onChange={(option) => handleFilterChange(configKey, option)} 
            value={filter[configKey] as string[]} 
            render={render ?? undefined}
          />
        )
      case 'option':
        return (
          <OptionFilter 
            className={itemClass} 
            label={label} 
            items={type.options} 
            onChange={(option) => handleFilterChange(configKey, option)} 
            value={filter[configKey] as string} 
            />
        )
      case 'rangeSlider':
      case 'rangeSingle':
        return <RangeFilter 
                type={type.type} 
                label={label} 
                className={itemClass} 
                min={type.min} 
                max={type.max} 
                onChange={(value) => handleFilterChange(configKey, value)} 
                value={filter[configKey] as number | number[]} 
                />
    }
  }
  return (
    <div className='relative'>
      <Popover
        placement='bottomLeft'
        trigger={'click'}
        content={(
          <div className='min-w-[350px] min-h-[300px] px-4 pb-2 flex flex-col'>
            <div className='grow'>
              <Typography.Title level={5} className='mt-1'>
                Filter content
              </Typography.Title>
              {Object.keys(configs).map((configKey) => {
                const config = configs[configKey] as App.Atoms.TableAdvance.TableFilterConfigItem
                return (
                  <div key={configKey} className='my-3'>
                    {renderFilterComponent(config, configKey as keyof TData)}
                  </div>
                )
              })}
            </div>

            <div className='flex gap-3 items-center justify-center mt-6'>
              <Button className='px-4' onClick={handleClearAll}>Clear all</Button>

              <Button type='primary' className='px-6' onClick={handleApplyFilter}>Apply</Button>
            </div>
          </div>
        )}
        open={open}
        onOpenChange={setOpen}
      >
        <Button>
          <div className={`flex items-center gap-1 ${filterCount ? '' : 'pr-2'}`}>
            <Icon icon={'mage:filter'} className='rotate-90' />
            <span>Filter</span>
            {!!filterCount && (
              <span className='text-blue-500 ml-1'>{filterCount}</span>
            )}
          </div>
        </Button>
      </Popover>
    </div>
  )
}

export const Filter = React.memo(React.forwardRef(TableFilterComponent)) as <TData extends object>(
  props: React.PropsWithoutRef<App.Atoms.TableAdvance.TableFilterProps<TData>> & React.RefAttributes<App.Atoms.TableAdvance.TableFilterRef<TData>>
) => ReturnType<typeof TableFilterComponent>
