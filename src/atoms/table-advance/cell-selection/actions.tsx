import { MouseEventHand<PERSON>, useCallback, useId, useMemo } from 'react';
import { Icon } from '@iconify/react';

import { Input } from '@/atoms';
import { cn, readClipboardData } from '@/utils';

const borderStyle = 'border-[1px] border-slate-200';
const shapeStyle = 'rounded-2xl p-[4px]';
// const bgGradient = 'bg-gradient-to-r from-indigo-100/30 from-10% via-sky-100/30 via-30% to-emerald-100/30 to-90%';
const backgroundStyle = 'bg-white/30 backdrop-blur-md shadow-default';
const containerClass = cn(
  'absolute select-none z-50 hidden',
  borderStyle,
  // bgGradient,
  backgroundStyle,
  shapeStyle
);

type Item = {
  icon: string;
  iconClass?: string;
  title: React.ReactNode;
  value: string;
  onClick?: EventListener | MouseEventHandler<HTMLDivElement>;
}


type ActionViewAPI = {
  close: () => void;
}

type Options = {
  onSelect: (
    api: ActionViewAPI,
    action: 'copy' | 'paste' | 'delete' | 'select-current-col' | 'select-current-row' | 'select-to-row',
    ...args: any[]
  ) => void;
}

export function useActionsView(options?: Options) {
  const { onSelect } = options || {};
  const viewId = useId();

  const api = useMemo<ActionViewAPI>(() => ({
    close: () => {
      const view = document.getElementById(viewId);
      !view?.classList.contains('hidden') && view?.classList.add('hidden')
    }
  }), [viewId])

  const onPaste = useCallback<EventListener>(() => {
    readClipboardData().then((value) => {
      onSelect && onSelect(api, 'paste', value);
    });
  }, [onSelect, api]);

  const onSelectRows = useCallback(() => {
    onSelect && onSelect(api, 'select-current-row');
  }, [onSelect, api])

  const items = useMemo<Array<Item>>(() => ([
    { icon: 'qlementine-icons:copy-16', iconClass: 'text-green-500', value: 'copy', title: 'Sao chép' },
    {
      icon: 'qlementine-icons:paste-16',
      iconClass: 'text-blue-500',
      value: 'paste',
      title: 'Dán dữ liệu đã sao chép',
      onClick: onPaste
    },
    { icon: 'qlementine-icons:trash-16', iconClass: 'text-red-500', value: 'delete', title: 'Xóa' },
    { icon: 'qlementine-icons:arrows-up-down-12', value: 'select-current-col', title: 'Chọn cả cột' },
    {
      icon: 'qlementine-icons:arrows-left-right-12',
      value: 'select-current-row',
      title: 'Chọn cả hàng',
      onClick: onSelectRows
    },
    {
      icon: 'qlementine-icons:mouse-selection-16',
      iconClass: 'text-orange-500', 
      value: 'select-to-row',
      title: (
        <div className='flex gap-2 items-center'>
          <span>Chọn từ hàng</span>
          <Input className='w-[40px] text-center' size='small' />
          <span>đến</span>
          <Input className='w-[40px] text-center' size='small' />
        </div>
      )
    },
  ]), [onPaste]);

  const view = useMemo(() => (
    <div id={viewId} className={cn(containerClass)}>
      <div className='px-[6px] min-w-[100px] min-h-[100px]'>
        {items.map(item => {

          return (
            <div
              key={item.value}
              className='flex items-center gap-2 pl-3 pr-4 h-[36px] rounded-[12px] bg-white border my-1 hover:bg-slate-50 cursor-pointer hover:border-blue-400'
              onClick={(item.onClick || api.close) as MouseEventHandler}
            >
              <Icon icon={item.icon} className={item.iconClass || 'text-gray-600'}/>
              <span className='text-nowrap'>{item.title}</span>
            </div>
          )
        })}
      </div>
    </div>
  ), [viewId, items])

  return { viewId, view, api };
}
