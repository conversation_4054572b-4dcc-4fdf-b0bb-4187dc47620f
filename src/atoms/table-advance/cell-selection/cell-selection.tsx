import {
  GridReadyEvent,
  AgEventListener,
  GridApi,
  CellMouseDownEvent,
  CellMouseOverEvent,
  ColDef,
} from 'ag-grid-community';
import React, { useCallback, useRef, useTransition } from 'react';
import lodash from 'lodash';

import { useActionsView } from './actions';
import './style.css';

const SELECTED_BG = 'app-cell-selected';
const SELECTED_LEFT = 'border-l-red-500',
      SELECTED_RIGHT = 'border-r-red-500',
      SELECTED_TOP = 'border-t-red-500',
      SELECTED_BOTTOM = 'border-b-red-500';

type AgMouseEvent<T> = CellMouseDownEvent<T> | CellMouseOverEvent<T> | CellMouseDownEvent<T>;

function getRowsBetween(api: GridApi, startRowId: string, endRowId: string) {
  const rows = new Set<string>();
  let isBetween = false;
  let shouldCollect = false;
  api.forEachNode(rowNode => {
    if (startRowId == endRowId && rowNode.id === startRowId) {
      rows.add(rowNode.id as string);
    } else {
      if (rowNode.id === startRowId || rowNode.id === endRowId) {
        shouldCollect = isBetween;
        isBetween = !isBetween;
      }

      if (isBetween || shouldCollect) {
        if (!isBetween && shouldCollect) shouldCollect = false;
        rows.add(rowNode.id as string);
      }
    }
  })

  return Array.from(rows);
}

type SelecteAreaRect = Omit<DOMRect, 'toJSON'> & {
  topLeft?: HTMLElement;
  bottomRight?: HTMLElement;
  topRight?: HTMLElement;
  bottomLeft?: HTMLElement;
};

function getSelectedAreaRect(elements: HTMLElement[]): SelecteAreaRect {
  let left = Infinity, right = 0, top = Infinity, bottom = 0, x = Infinity, y = Infinity;
  let topLeft: HTMLElement | undefined = undefined,
    bottomRight: HTMLElement | undefined = undefined,
    topRight: HTMLElement | undefined = undefined,
    bottomLeft: HTMLElement | undefined = undefined;

  elements.forEach(element => {
    const rect = element.getBoundingClientRect();
    if (rect.left < left) left = rect.left;
    if (rect.top < top) top = rect.top;
    if (rect.right > right) right = rect.right;
    if (rect.bottom > bottom) bottom = rect.bottom;
    if (rect.x < x) x = rect.x;
    if (rect.y < y) y = rect.y;
    if (rect.left === left && rect.top === top) topLeft = element;
    if (rect.bottom === bottom && rect.right === right) bottomRight = element;
    if (rect.bottom === bottom && rect.left === left) bottomLeft = element;
    if (rect.top === top && rect.right === right) topRight = element;
  });

  return {
    left, right, bottom, top,
    width: right - left,
    height: bottom - top,
    x, y,
    topLeft,
    bottomRight,
    topRight,
    bottomLeft
  };
}

type Point = { x: number, y: number, [key: string | number]: any };
type Distance = { x: number; y: number; l: number };
function getPointThatNearBy(o: Point, points: Point[]): Distance {
  const distances = points.map(p => ({
    x: p.x,
    y: p.y,
    l: Math.sqrt(Math.pow((o.x - p.x), 2) + Math.pow(o.y - p.y, 2)),
    ...lodash.pick(p, 'top', 'left', 'right', 'bottom', 'width', 'height')
  }));
  distances.sort((a, b) => a.l - b.l);
  return distances[0];
}

export function useCellSelection<TData extends object>(
  container: React.RefObject<HTMLElement>,
  options?: App.Atoms.TableAdvance.CellSelectionOptions
) {
  const selectedCols = useRef<Set<string>>(new Set());
  const selectedRows = useRef<Set<string>>(new Set());
  const selecting = useRef<boolean>(false);
  const editing = useRef<boolean>(false);
  const api = useRef<GridApi<TData>>();
  const startPosition = useRef<{ colId: string, rowId: string } | undefined | null>();
  const [_, startTransition] = useTransition();
  const selectedRect = useRef<SelecteAreaRect>();
  const prevEvent = useRef<Event | null>();

  const upudateCellsBorder = useCallback((cells: HTMLDivElement[], areaRect: SelecteAreaRect) => {
    const { top, left, right, bottom } = areaRect;

    cells.forEach(cell => {
      const rect = cell.getBoundingClientRect();
      if (rect.left === left && !cell.classList.contains(SELECTED_LEFT)) {
        cell.classList.add(SELECTED_LEFT);
      }

      if (rect.right === right && !cell.classList.contains(SELECTED_RIGHT)) {
        cell.classList.add(SELECTED_RIGHT);
      }

      if (rect.top === top && !cell.classList.contains(SELECTED_TOP)) {
        cell.classList.add(SELECTED_TOP);
      }

      if (rect.bottom === bottom && !cell.classList.contains(SELECTED_BOTTOM)) {
        cell.classList.add(SELECTED_BOTTOM);
      }
    })
  }, [container]);

  const updateSelectedCellsByArea = useCallback((params?: {rowIds: string[], colIds: string[] }): {
    cells: HTMLElement[],
    rect: SelecteAreaRect
  } => {
    const { rowIds = Array.from(selectedRows.current), colIds = Array.from(selectedCols.current) } = params || {};

    const cells: HTMLDivElement[] = [];

    for (let rowId of rowIds) {
      const allRows = container.current?.querySelectorAll(`div[row-id="${rowId}"]`);
      allRows?.forEach(row => {
        for (let colId of colIds) {
          const cell = row.querySelector(`div[col-id="${colId}"]`);
          !cell?.classList.contains(SELECTED_BG) && cell?.classList.add(SELECTED_BG);
          if (cell) {
            (cell as HTMLDivElement).dataset.markSelected = "1";
            cells.push(cell as HTMLDivElement);
          }
        }
      })
    }

    const rect = getSelectedAreaRect(cells);

    upudateCellsBorder(cells, rect);
    return { cells, rect };
  }, [container, upudateCellsBorder]);

  const { viewId: actionsViewId, view: actions } = useActionsView({
    onSelect: (actionsViewApi, action) => {
      switch (action) {
        case 'select-current-row':
          const columns = api.current?.getColumnDefs();
          if (columns) {
            for (let col of columns) {
              const { field } = (col as ColDef<TData>) || {};
              field && selectedCols.current.add(field);
            }

            updateSelectedCellsByArea();
          }
          break;
      
        default:
          actionsViewApi.close();
          break;
      }
    }
  });

  const clear = useCallback((options: { resetData?: boolean } = { resetData: true }) => {
    if (options.resetData) {
      selectedCols.current.clear();
      selectedRows.current.clear();
    }
    const allSelectedCells = document.querySelectorAll('div[data-mark-selected="1"]');
    allSelectedCells.forEach(cell => {
      cell.classList.remove(SELECTED_BG);
      cell.classList.remove(SELECTED_LEFT);
      cell.classList.remove(SELECTED_RIGHT);
      cell.classList.remove(SELECTED_TOP);
      cell.classList.remove(SELECTED_BOTTOM);
    });
    const actionsView = document.getElementById(actionsViewId);
    if (actionsView && !actionsView.classList.contains('hidden')) {
      actionsView.classList.add('hidden');
    }
  }, [actionsViewId]);

  const getCellDiv = useCallback((params: AgMouseEvent<TData>) => {
    const { event } = params;
    if (!event) return;

    const target = event.target as HTMLElement;
    return target.closest('div[role="gridcell"]') as HTMLDivElement;
  }, [])

  const setStartPositionCell = useCallback((params: AgMouseEvent<TData>) => {
    const gridcell = getCellDiv(params);
    if (!gridcell) return;
    const colId = gridcell.getAttribute('col-id');
    const rowId = gridcell.closest('div[role="row"]')?.getAttribute('row-id');
    if (colId && rowId) startPosition.current = { colId, rowId };
  }, [getCellDiv]);

  const updateActionsView = useCallback((rect: SelecteAreaRect, event: MouseEvent) => {
    if (editing.current) return;
    
    const view = document.getElementById(actionsViewId);
    if (view?.classList.contains('hidden')) {
      view.classList.remove('hidden');
    }
    if (view && rect && container.current) {
      const containerRect = container.current.getBoundingClientRect();
      const viewRect = view.getBoundingClientRect();
      const topLeftRect = rect.topLeft?.getBoundingClientRect();
      const topRightRect = rect.topRight?.getBoundingClientRect();
      // const bottomLeftRect = rect.bottomLeft?.getBoundingClientRect();
      // const bottomRightRect = rect.bottomRight?.getBoundingClientRect();

      const point = getPointThatNearBy({ x: event.x, y: event.y }, [
        topLeftRect as Point,
        topRightRect as Point,
        // bottomLeftRect as Point,
        // bottomRightRect as Point
      ].filter(Boolean));

      const ACTION_MARGIN = 4;
      startTransition(() => {
        if (point) {
          const { top, left, width } = point as unknown as DOMRect;
          const isTopLeft = top === topLeftRect?.top && left === topLeftRect.left;
          const isTopRight = top === topRightRect?.top && left === topRightRect?.left;
          // const isBottomLeft = top === bottomLeftRect?.top && left === bottomLeftRect?.left;
          // const isBottomRight = top === bottomRightRect.top && left === bottomRightRect.left;

          let anchorLeft = 0, anchorTop = 0;
          if (isTopLeft) {
            anchorTop = top - containerRect.top + ACTION_MARGIN;
            anchorLeft = left - containerRect.left - viewRect.width - ACTION_MARGIN;
            const mustBeDisplayedInside = anchorLeft < 0;
            if (mustBeDisplayedInside) {
              anchorLeft = anchorLeft + viewRect.width + 2*ACTION_MARGIN;
            }
          } else if (isTopRight) {
            anchorTop = top - containerRect.top + ACTION_MARGIN;
            anchorLeft = left - containerRect.left + width + ACTION_MARGIN;
            const mustBeDisplayedInside = anchorLeft + containerRect.left + viewRect.width > screen.availWidth;
            if (mustBeDisplayedInside) {
              anchorLeft = anchorLeft - viewRect.width - 2 * ACTION_MARGIN;
            }
          }
          // else if (isBottomLeft) {
          //   anchorTop = top - containerRect.top + height - viewRect.height - ACTION_MARGIN;
          //   anchorLeft = left - containerRect.left + ACTION_MARGIN;
          // } else if (isBottomRight) {
          //   anchorTop = top - containerRect.top + height - viewRect.height - ACTION_MARGIN;
          //   anchorLeft = left - containerRect.left + width - viewRect.width - ACTION_MARGIN;
          // }

          view.style.top = `${anchorTop}px`;
          view.style.left = `${anchorLeft}px`;
        }
      })
    }
  }, [actionsViewId, container])

  const updateSelectedColsAndRows = useCallback((params: {
    rowIds: string[];
    colIds: string[];
    rowId: string;
  }) => {
    const { colIds, rowIds, rowId } = params;
    
    const oldRows = Array.from(selectedRows.current);
    const currentIndex = oldRows.indexOf(rowId);

    if (currentIndex === -1) { // new row selected
      rowIds.forEach(id => selectedRows.current.add(id));
    } else if (currentIndex < oldRows.length - 1) { // must delete rows
      for (let index = currentIndex; index < oldRows.length; index++) {
        selectedRows.current.delete(oldRows[index]);
      }
    }

    colIds.forEach(id => selectedCols.current.add(id));
  }, []);

  const calculateSelectedCell = useCallback((params: AgMouseEvent<TData>) => {
    const { api, event } = params;

    if (!startPosition.current) return;

    const { colId: startColId, rowId: startRowId } = startPosition.current;

    const gridcell = getCellDiv(params);
    if (!gridcell) return;

    const startColumn = api.getColumn<TData>(startColId);
    const startRow = api.getRowNode(startRowId);

    const endColId = gridcell.getAttribute('col-id');
    const endRowId = gridcell.closest('div[role="row"]')?.getAttribute('row-id');

    if (!endColId || !endRowId || !startColumn || !startRow) return;

    const endColumn = endColId === startColId ? startColumn : api.getColumn(endColId);
    const endRow = endRowId === startRowId ? startRow : api.getRowNode(endRowId);

    if (!endColumn || !endRow) return;

    const colums = api.getColumnDefs();
    if (!colums) return;

    const indexOfStart = colums.findIndex(col => startColId === (col as ColDef<TData>).colId);
    const indexOfLast = colums.findIndex(col => endColId === (col as ColDef<TData>).colId);

    const colIndexStart = Math.min(indexOfStart, indexOfLast);
    const colIndexEnd = Math.max(indexOfStart, indexOfLast);

    const colIds: string[] = [];

    for (let index = colIndexStart; index <= colIndexEnd; index++) {
      colIds.push((colums[index] as ColDef).colId as string);
    }

    const selectedRowIds = getRowsBetween(api, startRowId, endRowId);

    clear({ resetData: false });

    updateSelectedColsAndRows({ colIds, rowIds: selectedRowIds, rowId: endRowId });

    const { rect } = updateSelectedCellsByArea();

    selectedRect.current = rect;
    prevEvent.current = event;
  }, [getCellDiv, container, clear, updateSelectedCellsByArea, updateSelectedColsAndRows]);

  const showActionsView = useCallback((e: Event) => {
    if (selectedCols.current) {
      updateActionsView(
        selectedRect.current as SelecteAreaRect,
        e as MouseEvent
      );
    }
  }, [updateActionsView])

  const cellMouseOver = useCallback<AgEventListener<TData, any, 'cellMouseOver'>>(lodash.debounce((params) => {
    if (selecting.current) calculateSelectedCell(params);
  }, 10), [calculateSelectedCell]);

  const cellMouseDown = useCallback<AgEventListener<TData, any, 'cellMouseDown'>>((params) => {
    clear();

    if (editing.current) return;

    selecting.current = true;
    setStartPositionCell(params);
    calculateSelectedCell(params);
  }, [clear, setStartPositionCell]);

  const onMouseUp = useCallback<EventListener>((e) => {
    selecting.current = false;
    (selectedCols.current.size > 1 || selectedRows.current?.size > 1) && showActionsView(e);
    // showActionsView(e);
  }, [showActionsView]);

  const onColumnMoved = useCallback<AgEventListener<TData, any, 'columnMoved'>>(() => clear(), [clear])
  const onStartEditing = useCallback(() => editing.current = true, []);
  const onStopEditing = useCallback(() => setTimeout(() => editing.current = false), []);
  const onTableScroll = useCallback(lodash.debounce(() => {
    if (selectedCols.current.size > 1 || selectedRows.current.size > 1) {
      updateSelectedCellsByArea();
    }
  }, 30), [updateSelectedCellsByArea])

  const register = React.useCallback((params: GridReadyEvent<TData, any>) => {
    console.log('[Cell Selection] Register')
    api.current = params.api;
    params.api.addEventListener('cellMouseOver', cellMouseOver);
    params.api.addEventListener('cellMouseDown', cellMouseDown);
    params.api.addEventListener('columnMoved', onColumnMoved);
    params.api.addEventListener('cellEditingStarted', onStartEditing);
    params.api.addEventListener('cellEditingStopped', onStopEditing);
    params.api.addEventListener('bodyScroll', onTableScroll);

    const mainTable = container.current?.querySelector('div[role="presentation"]');
    if (mainTable) {
      mainTable.addEventListener('mouseup', onMouseUp);
    }

    if (container.current && !container.current.classList.contains('relative')) {
      container.current.classList.add('relative');
    }

    params.api.addEventListener('gridPreDestroyed', () => {
      console.log('[Cell Selection] Remove all listeners')
      api.current?.removeEventListener('cellMouseOver', cellMouseOver);
      api.current?.removeEventListener('cellMouseDown', cellMouseDown);
      api.current?.removeEventListener('columnMoved', onColumnMoved);
      api.current?.removeEventListener('cellEditingStarted', onStartEditing);
      api.current?.removeEventListener('cellEditingStopped', onStopEditing);
      api.current?.removeEventListener('bodyScroll', onTableScroll);

      const mainTable = container.current?.querySelector('div[role="presentation"]');
      if (mainTable) {
        mainTable.removeEventListener('mouseup', onMouseUp);
      }

      if (container.current?.classList.contains('relative')) {
        container.current.classList.remove('relative');
      }
    })
  }, [cellMouseOver, onMouseUp, cellMouseDown, onStartEditing, onStopEditing, container]);

  return { register, actionsPlaceholder: actions };
}
