import React, { ClipboardEventHandler, useCallback, useEffect } from 'react';

import { readClipboard } from '@/utils';

export const useClipboard = (container: React.RefObject<HTMLElement>) => {

  const onPaste = useCallback<ClipboardEventHandler>((e) => {
    console.log('paste')
    readClipboard(e);
  }, [])

  const register = useCallback(() => {
    const mainTable = container.current?.querySelector('div[role="presentation"]');
    if (mainTable) {
      // mainTable.addEventListener('paste', onPaste as unknown as EventListener);
    }
  }, [container, onPaste]);

  useEffect(() => {
    return () => {
      const mainTable = container.current?.querySelector('div[role="presentation"]');
      if (mainTable) {
       // mainTable.removeEventListener('paste', onPaste as unknown as EventListener);
      }
    }
  }, [container]);

  return { register };
}
