declare namespace App.Atoms.TableAdvance {
  type SelectProps = import("antd").SelectProps;
  type AgGridReactProps<T> = import("ag-grid-react").AgGridReactProps<T>;
  type AgGridReact<T> = import("ag-grid-react").AgGridReact<T>;
  type CustomHeaderProps<T = object> = import("ag-grid-react").CustomHeaderProps<T>;
  type CustomCellRenderProps<T> = import("ag-grid-react").CustomCellRendererProps<T>;
  type ColumnState = import("ag-grid-community").ColumnState;
  type ColDef<T extends object = {}> = import("ag-grid-community").ColDef<T>;
  type GridApi<T> = import('ag-grid-community').GridApi<T>;
  type Dayjs = import('dayjs').Dayjs;

  export type HeaderProps = {
    icon?: string | JSX.Element;
    containerClass?: string;
    headerNameClass?: string;
  } & CustomHeaderProps;

  export type FilterData<TData extends object> = {
    [key in keyof TData]?: unknown;
  };
  export type OptionFilterType = {
    type: "option";
    options: Array<{ label: string; value: string | number }>;
    props?: SelectProps;
  };

  export type CheckboxFilterType = {
    type: "checkbox";
    options: Array<{ label: string; value: string | number }>;
  };

  export type RadioFilterType = {
    type: "radio";
    options: Array<{ label: string; value: string | number }>;
  };
  export type NumberFilterType = {
    type: "number";
    min: number;
    max: number;
    step?: number;
  };
  export type RangeFilterType = {
    type: "rangeSingle" | "rangeSlider";
    min: number;
    max: number;
  };

  export type TableFilterConfigItem = {
    type:
      | "text"
      | "date"
      | "rangeDate"
      | NumberFilterType
      | CheckboxFilterType
      | RadioFilterType
      | OptionFilterType
      | RangeFilterType;
    label: string;
    value?:
      | undefined
      | null
      | string
      | number
      | boolean
      | { from: number; to: number };
    render?: (
      options: Array<{ label: string; value: number | string }>,
      label: string,
      value: string[],
      onChange?: (values: string[]) => void
    ) =>
      | string
      | number
      | boolean
      | React.ReactNode
      | JSX.Element
      | undefined
      | null;
  };
  export type TableFilterProps<TData extends object> = {
    configs: {
      [key: string]: TableFilterConfigItem | undefined;
    };
    onChange?: (filter?: FilterData<TData>) => void;
    defaultData?: FilterData<TData> | (() => FilterData<TData>);
  };
  export type TableFilterRef<TData extends object> = {
    show(): void;
    hide(): void;
    getFilter(): FilterData<TData>;
  };
  type DateFilter = [
    date:
      | Dayjs
      | App.Atoms.TableAdvance.NoUndefinedRangeValueType<Dayjs>
      | [Dayjs, Dayjs],
    dateString: string | string[] | [string, string]
  ]
  export type TableFilter<TData extends object> = {
    start: number;
    end: number;
    keyword?: string;
    sort?: {
      [key in keyof TData]?: "asc" | "desc";
    };
    filter?: FilterData<TData>;
    createdBefore?: string;
    createdAfter?: string;
  };
  export type FetchTableDataResult<TData extends object> = {
    start: number;
    end: number;
    total: number;
    items: Array<TData>;
  };

  export type FetchDataFn<TData extends object> = (
    filter?: TableFilter<TData>
  ) => Promise<FetchTableDataResult<TData>>;

  export type GridRowActionOptions = {
    tableRef?: React.RefObject<AgGridReact<TData>>
    rowActionsRef?: React.RefObject<RowActionsRef> | null
  }
  
  export type RowActionConfig<TData extends object> = Omit<App.Atoms.ButtonProps, 'onClick'> & {
    onClick?: ( data: TData | null, options?: GridRowActionOptions) => void
  }

  export type CellSelectionApi = {};
  export type CellSelectionOptions = {
    onPaste?: (api: CellSelectionApi) => void;
  };

  export type TableProps<TData extends object> = {
    type?: 'table' | 'list'
    hideTableTools?: boolean;
    hideListBorder?: boolean;
    fetchData?: FetchDataFn<TData>;
    filterConfigs?: Record<string, TableFilterConfigItem>;
    defaultFilterData?: FilterData<TData> | (() => FilterData<TData>);
    tableId: string;
    containerClassName?: string;
    rowAction?: {
      configs: RowActionConfig<TData>[]
      className?: string
    }
    selectionBarActions?: SelectionBarAction<TData>[];
    onSelectionChanged?: (data: TData[]) => void;
    suspendCellSelection?: boolean; // Enable/Disable Cell selection. Default: true
    cellSelectionOptions?: CellSelectionOptions;
  } & AgGridReactProps<TData>;

  export type TableRef<TData extends object = {}> = {
    refresh(): void;
    focus(): void;
    setFilter: (filter?: FilterData<TData> | ((filter?: FilterData<TData>) => FilterData<TData>)) => void;
    gridApi(): GridApi<TData> | undefined;
  };

  export type CustomColumnState = ColumnState & { displayName: string };

  export type TableSettingProps = {
    columnState: CustomColumnState[];
    onColumnDisplayChange?: (info: { colId: string; hide: boolean }) => void;
  };

  export type TableSettingRef = {};

  export type NoUndefinedRangeValueType<DateType> = [
    start: DateType | null,
    end: DateType | null
  ];

  export type SelectionBarAction<TData extends object> = Omit<App.Atoms.ButtonProps, 'onClick'> & {
    onClick?: (rowData: TData[]) => void
  }

  export type SelectionBarRef = {
    show(): void
    hide(): void
  };

  export type SelectionBarProps<TData extends object> = {
    className?: string
    rowCount?: number
    rowData?: TData[]
    actions?: SelectionBarAction<TData>[]
  };

  export type RowActionsRef = {
    setPosition: (position: { x: number, y: number }) => void
    show: () => void
    hide: () => void
    getConfigRef: () => HTMLDivElement
  };

  export type RowActionsProps<TData extends object> = {
    className?: string
    configs: RowActionConfig<TData>[]
    tableRef: React.RefObject<AgGridReact<TData>>
    getRowHoverData: () => TData | null
  };

  export type RowActionConfig<TData extends object> = {
    icon?: ReactNode
    onClick?: (type: string, data: TData) => void
  }
}
