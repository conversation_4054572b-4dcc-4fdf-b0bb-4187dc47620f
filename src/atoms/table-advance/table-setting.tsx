import React, { useState } from "react";
import { theme } from "antd";
import Button from "../button";
import Popover from "../popover";
import Switch from "../switch/Switch";
import Typography from "../typography";
import { Icon } from "@iconify/react";

const TableSettingComponent = (
  props: App.Atoms.TableAdvance.TableSettingProps,
  ref: React.Ref<App.Atoms.TableAdvance.TableSettingRef>
) => {
  const { onColumnDisplayChange, columnState } = props;
  const { token } = theme.useToken();

  const [newColumnState, setNewColumnState] = useState(columnState);

  React.useImperativeHandle(ref, () => ({}), []);

  const handlerOnChange = React.useCallback(
    (colId: string, index: number) =>
      (checked: boolean): void => {
        if (!onColumnDisplayChange) {
          return;
        }

        onColumnDisplayChange({ colId, hide: !checked });

        newColumnState[index].hide = !checked;
        setNewColumnState([...newColumnState]);
      },
    [newColumnState]
  );

  React.useEffect(() => {
    setNewColumnState(columnState);
  }, [columnState]);

  const popoverContent = React.useMemo(() => {
    return (
      <div className="min-w-[200px] flex flex-col gap-2 p-2">
        <div>
          <Typography.Title level={5} className="mt-1 mb-0">
            Tuỳ chọn hiển thị
          </Typography.Title>
          <Typography.Text className="mt-0" type="secondary">
            Hiển thị hoặc ẩn cột trong bảng, có thể sắp xếp lại thứ tự cột
          </Typography.Text>
        </div>
        <div className="flex flex-col gap-2 max-h-[50vh] pr-2 -mr-4 overflow-y-auto">
          {columnState.map((item, index) => (
            <div
              className="flex items-center justify-between gap-2 w-full"
              key={`${item.colId}`}
            >
              <div className="flex items-center gap-1">
                <Typography.Text>{item.displayName}</Typography.Text>
              </div>

              <Switch
                size="small"
                onChange={handlerOnChange(item.colId, index)}
                value={!item.hide!}
                key={`${item.colId}`}
              />
            </div>
          ))}
        </div>
      </div>
    );
  }, [newColumnState, columnState]);

  return (
    <Popover trigger={"click"} placement="bottomRight" content={popoverContent}>
      <Button className="flex items-center px-2">
        <Icon
          icon="lets-icons:setting-fill"
          fontSize={18}
          style={{ color: token.colorTextSecondary }}
        />
      </Button>
    </Popover>
  );
};

export const TableSetting = React.memo(
  React.forwardRef(TableSettingComponent)
) as (
  props: React.PropsWithoutRef<App.Atoms.TableAdvance.TableSettingProps> &
    React.RefAttributes<App.Atoms.TableAdvance.TableSettingRef>
) => ReturnType<typeof TableSettingComponent>;
