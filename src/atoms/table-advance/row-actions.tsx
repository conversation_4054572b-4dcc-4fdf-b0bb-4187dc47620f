import React from "react";
import { cn } from "@/utils";
import { theme } from "antd";

import { Button } from "@/atoms";

const RowActions = <TData extends object>(
  props: App.Atoms.TableAdvance.RowActionsProps<TData>,
  ref: React.Ref<App.Atoms.TableAdvance.RowActionsRef>
) => {
  const { className, configs, getRowHoverData, tableRef } = props;
  const { token } = theme.useToken();

  const [hidden, setHidden] = React.useState(true);
  const [position, setPosition] = React.useState<{ x: number, y: number } | null>(null);
  const configRef = React.useRef<HTMLDivElement>(null);

  React.useImperativeHandle(ref, () => ({
    setPosition: (position: { x: number, y: number }) => {
      setPosition(position);
    },
    show: () => {
      setHidden(false);
    },
    hide: () => {
      setHidden(true);
    },
    getConfigRef: () => {
      return configRef.current as HTMLDivElement;
    }
  }));

  return (
    <>
      {configs.length > 0 && (
        <div
          ref={configRef}
          className={cn(
            "px-2 flex items-center gap-2 fixed z-10 rounded-3xl mt-[3px] h-[30px] shadow-lg",
            hidden && "opacity-0",
            className
          )}
          style={{ 
            background: token.colorBgBase,
            top: position?.y,
            left: position?.x,
          }}
        >
          {getRowHoverData && configs.map((action, index) => {
            const {icon, onClick, ...restProps} = action
            const data = getRowHoverData();
            return (
              <Button
                key={index}
                icon={action.icon}
                onClick={() => action.onClick?.(data, { tableRef: tableRef, rowActionsRef: ref as React.RefObject<App.Atoms.TableAdvance.RowActionsRef>})}
                className="rounded-full p-0 w-[24px] h-[24px] [&_span]:flex [&_span]:items-center [&_span]:justify-center"
                {...restProps}
              />
            )
          })}
        </div>
      )}
    </>
  );
};

RowActions.displayName = "RowActions";

export default React.memo(React.forwardRef(RowActions)) as <
  TData extends object
>(
  props: React.PropsWithoutRef<App.Atoms.TableAdvance.RowActionsProps<TData>> &
    React.RefAttributes<App.Atoms.TableAdvance.RowActionsRef>
) => ReturnType<typeof RowActions>;
