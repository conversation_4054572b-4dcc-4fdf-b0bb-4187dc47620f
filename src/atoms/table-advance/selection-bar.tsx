import React from "react";
import { Icon } from "@iconify/react";
import { cn } from "@/utils";
import { theme, Button } from "antd";
import Typography from "../typography";

const SelectionBar =  <TData extends object>(
  props: App.Atoms.TableAdvance.SelectionBarProps<TData>,
  ref: React.Ref<App.Atoms.TableAdvance.SelectionBarRef>
) => {
  const { className, rowCount, actions, rowData } = props;

  const { token } = theme.useToken();

  const [visible, setVisible] = React.useState(false);

  React.useImperativeHandle(ref, () => ({
    show: () => {
      setVisible(true);
    },
    hide: () => {
      setVisible(false);
    }
  }));

  return (
    <>
      {visible && (
        <div
          className={cn(
            className,
            "shadow-xl rounded-lg px-[30px] py-2 border"
          )}
          style={{
            background: token.colorBgBase
          }}
        >
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-500 mr-4 flex gap-1 items-center">
              <Icon
                icon="fluent:checkbox-checked-24-filled"
                width="20"
                height="20"
                style={{ color: token.colorPrimary }}
              />
              <Typography.Text>{rowCount} hàng đã chọn</Typography.Text>
            </div>
            <div className="flex items-center gap-4">
              {rowData && actions?.map((action, index) => {
                const { onClick, ...rest } = action

                return (
                  <Button 
                    key={index}
                    onClick={() => action.onClick?.(rowData)}
                    {...rest}
                  />
                )
              })}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

SelectionBar.displayName = "SelectionBar";
export default React.memo(React.forwardRef(SelectionBar)) as <
  TData extends object
>(
  props: React.PropsWithoutRef<App.Atoms.TableAdvance.SelectionBarProps<TData>> &
    React.RefAttributes<App.Atoms.TableAdvance.SelectionBarRef>
) => ReturnType<typeof SelectionBar>;
