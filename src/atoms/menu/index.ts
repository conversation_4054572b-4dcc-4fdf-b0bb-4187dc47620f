import React from "react";
import BaseComponent from "../../components/base"; // Importing base class for reuse
import { MenuItemDetail } from "../menu-item"; // Importing MenuItemDetail to be used in the component
import MenuView from "./View"; // Importing the view component for rendering

export default class MenuComponent extends BaseComponent {
    view: React.ReactNode; // The view to be rendered, which will contain menu items
    #ref: React.RefObject<App.Components.MenuRef>; // A reference to the Menu component for potential future use
    menuItems: MenuItemDetail[] = []; // Array to hold the instances of MenuItemDetail

    // Constructor to initialize the MenuComponent
    constructor(app: App.Application, props: App.Components.MenuProps) {
        super(app); // Call the constructor of the parent BaseComponent
        this.#ref = React.createRef(); // Initialize the reference to the Menu component

        // Create the list of MenuItemDetail instances based on props.menuItems
        this.menuItems = props.menuItems.map((menuItem, index) => {
            // For each menu item, create a new MenuItemDetail instance
            return new MenuItemDetail(app, {
                item: menuItem, // Pass the current menu item as a prop
                index, // Pass the index as a prop
                onClick: props.onClick || (() => {}), // Pass the onClick handler if provided, otherwise use a no-op function
            });
        });

        // Create the view for MenuComponent, which will include the individual MenuItemDetail views
        this.view = React.createElement(MenuView, {
            ...props, // Spread the remaining props (className, etc.)
            ref: this.#ref, // Pass the reference to the MenuView
            menuItemView: this.menuItems.map((menuItem) => menuItem.view), // Pass the array of MenuItemDetail views
        });
    }
}
