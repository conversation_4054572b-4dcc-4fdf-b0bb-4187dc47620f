import React from "react";

// MenuView component for rendering the menu structure.
const MenuView = React.forwardRef<
    App.Components.MenuRef, // Type for the ref
    App.Components.MenuProps // Type for the props
>(({ menuItemView, className }, _) => {
    return (
        <div className={`flex flex-col p-6 ${className}`}>
            {/* 
                Conditional rendering for menuItemView:
                - If menuItemView is an array, it maps over the array and clones each React element to add a unique "key" prop.
                - If menuItemView is not an array or is invalid, it renders nothing (null).
            */}
            {menuItemView && Array.isArray(menuItemView)
                ? // Map over each child element in the array and ensure each has a unique key
                  menuItemView.map(
                      (child, index) =>
                          React.isValidElement(child) // Check if the child is a valid React element
                              ? React.cloneElement(child, { key: index }) // Clone the element and assign a unique key
                              : child // If it's not a React element, return as is
                  )
                : null}
        </div>
    );
});

// Set displayName to make the component name clear in DevTools
MenuView.displayName = "MenuView";

// Export the component as default
export default MenuView;
