import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react"; // Importing <PERSON><PERSON> and <PERSON>Obj for Storybook configuration
import MenuComponent from "."; // Importing the MenuComponent
import app from "../../stories/index"; // Importing the app instance to pass into the component
import { Icon } from ".."; // Importing the Icon component

// Sample data for menu items. This includes items, sub-items, and groupings.
const sampleMenuItems: App.Components.MenuItemDetail[] = [
    {
        key: "4",
        type: "item",
        title: "Typography",
        icon: <Icon icon="line-md:bell" />,
        href: "a",
    },
    {
        key: "1",
        type: "subitem",
        title: "Typography",
        icon: <Icon icon="line-md:bell" />,
        subIcon: <Icon icon="mdi:folder-add-outline" />,
        defaultOpen: true,
        children: [
            {
                key: "1-1",
                type: "item",
                title: "Typography",
                icon: <Icon icon="line-md:bell" />,
                href: "a1",
            },
            {
                key: "1-2",
                type: "item",
                title: "Typography",
                icon: <Icon icon="line-md:bell" />,
                href: "a2",
            },
            {
                key: "1-3",
                type: "item",
                title: "Typography",
                icon: <Icon icon="line-md:bell" />,
                href: "a3",
            },
        ],
    },
    {
        type: "divider", // Divider to separate items visually
    },
    {
        key: "2",
        type: "group",
        title: "Typography",
        icon: <Icon icon="line-md:bell" />,
        children: [
            {
                key: "1-1",
                type: "subitem",
                title: "Typography",
                icon: <Icon icon="line-md:bell" />,
                href: "a4",
            },
            {
                key: "1-2",
                type: "item",
                title: "Typography",
                icon: <Icon icon="line-md:bell" />,
                href: "a5",
            },
            {
                key: "1-3",
                type: "item",
                title: "Typography",
                icon: <Icon icon="line-md:bell" />,
                href: "a6",
            },
        ],
    },
    {
        type: "divider", // Divider to separate items visually
    },
    {
        key: "3",
        type: "item",
        title: "Typography",
        icon: <Icon icon="line-md:bell" width="24" height="24" />,
        href: "a7",
    },
    {
        key: "4",
        type: "item",
        title: "Typography",
        icon: <Icon icon="line-md:bell" width="24" height="24" />,
        href: "a8",
    },
];

// MenuView component to render MenuComponent
const MenuView = (props: Partial<App.Components.MenuProps>) => {
    // Event handler for when a menu item is clicked
    const handleMenuItemClick = (item: App.Components.MenuItemDetail) => {
        console.log("Clicked item:", item); // Log clicked item to the console
    };

    // Creating an instance of MenuComponent and passing the menu items and onClick handler
    const menuInstance = new MenuComponent(app, {
        menuItems: props.menuItems || [], // Use props.menuItems if available, otherwise fallback to an empty array
        onClick: handleMenuItemClick, // Pass the onClick handler to handle click events
    });

    // Render the menu component's view
    return <div>{menuInstance.view}</div>;
};

// Storybook metadata for the Menu component
const meta = {
    title: "Atoms/Menu", // Title in the Storybook menu
    component: MenuView, // The component to display in Storybook
    tags: ["autodos"], // Tags for Storybook
} satisfies Meta<typeof MenuView>;

export default meta; // Export metadata for Storybook

// Type for the story based on the metadata
type Story = StoryObj<typeof meta>;

// Default story configuration for Storybook
export const Default: Story = {
    args: {
        menuItems: sampleMenuItems, // Pass the sample menu items to the Menu component in the story
    },
};
