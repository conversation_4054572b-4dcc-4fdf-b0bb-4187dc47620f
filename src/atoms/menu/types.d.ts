declare namespace App {
    declare module Components {
        // Define the possible types for a menu item.
        // 'item' is a standard menu item, 'subitem' is a nested item,
        // 'group' represents a grouping of items, and 'divider' is a separator.
        export type MenuItemType = "item" | "subitem" | "group" | "divider";

        // The structure for each menu item.
        export type MenuItemDetail = {
            // Unique identifier for the menu item (optional).
            key?: string;

            // Type of the menu item: could be "item", "subitem", "group", or "divider".
            type: MenuItemType;

            // The title of the menu item, displayed to the user.
            title?: string;

            // The main icon to display next to the item title.
            icon?: ReactNode;

            // A secondary icon (e.g., for a sub-item).
            subIcon?: ReactNode;

            // Flag to indicate if the item should be expanded by default (only for 'subitem' type).
            defaultOpen?: boolean;

            // Children for 'subitem' or 'group' type items. These are the nested menu items.
            children?: MenuItemDetail[];

            // The hyperlink reference for the item (if any).
            href?: string;
        };

        // Props for MenuItemDetail component. This defines how the menu item is rendered.
        export type MenuItemDetailProps = {
            // The menu item data, passed from parent to this component.
            item: MenuItemDetail;

            // The index of the current item in the array of items (used for rendering).
            index: number;

            // The callback function to handle when a menu item is clicked.
            onClick: (item: MenuItemDetail) => void;
        };

        // The ref type for MenuItemDetail. This can be expanded if necessary.
        export type MenuItemDetailRef = {};

        // Props for the main Menu component.
        export type MenuProps = {
            // Optional custom view for rendering menu items. Can be passed from the parent.
            menuItemView?: React.ReactNode;

            // Optional CSS class name for styling the menu component.
            className?: string;

            // The array of menu items to display in the menu.
            menuItems: MenuItemDetail[];

            // Optional callback function for handling click events on menu items.
            onClick?: (item: MenuItemDetail) => void;
        };

        // Ref type for the Menu component. This can be expanded for additional functionality.
        export type MenuRef = {};
    }
}
