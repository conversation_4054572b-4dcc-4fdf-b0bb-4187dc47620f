import type { <PERSON>a, StoryObj } from "@storybook/react";
import {CustomIcon} from "./Icon";
const meta = {
  title: 'Atoms/Icon',
  component: CustomIcon,
  tags: ["autodocs"],
  argTypes: {
    name: { control: "text", defaultValue: "iconamoon:file-thin" },
    size: { control: "number", defaultValue: 24 },
  },
} satisfies Meta<typeof CustomIcon>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    icon: "iconamoon:file-thin",
    size: 24,
  },
};

export const AnotherIcon: Story = {
  args: {
    icon: "line-md:heart-filled",
    size: 24,
  },
};
