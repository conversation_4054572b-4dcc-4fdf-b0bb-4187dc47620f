import type { Meta, StoryObj } from '@storybook/react'
import { ImageText } from './ImageText'

const meta: Meta<typeof ImageText> = {
  title: 'Atoms/ImageText',
  component: ImageText,
  tags: ['autodocs']
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    src: 'https://mansa.com.vn/wp-content/uploads/2020/11/mansa-1.jpg',
    alt: 'Placeholder Image',
    text: '<PERSON><PERSON>',
    imageWidth: '14px',
    imageHeight: '14px'
  }
}

export const Outline: Story = {
  args: {
    ...Default.args,
    variant: 'outline'
  }
}

export const Tag: Story = {
  args: {
    ...Default.args,
    variant: 'tag'
  }
}

export const Hover: Story = {
  args: {
    ...Default.args,
    state: 'hover'
  }
}
