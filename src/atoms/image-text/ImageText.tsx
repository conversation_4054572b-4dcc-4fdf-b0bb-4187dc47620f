import React, { useCallback } from 'react'
import { cn } from '../../utils'
import Image from '../image/index'
import './style.css'
import { Icon } from '..'
import { Text } from '../typography/text'

export const ImageText: React.FC<Atoms.ImageTextProps> = ({
  text,
  variant = 'default',
  state = 'normal',
  imageWidth = '16px',
  imageHeight = '16px',
  imageClassName = '',
  textClassName = '',
  className,
  onCloseTag,
  ...props
}) => {
  const handleCloseTag = useCallback(() => {
    if (onCloseTag) {
      onCloseTag()
    }
  }, [onCloseTag])

  const baseStyles = 'flex items-center'

  const variantStyles = {
    default: '',
    outline: 'border border-stroke border-solid',
    tag: 'border border-stroke border-solid'
  }[variant]

  const stateStyles = state === 'hover' ? 'hover:bg-secondary' : ''

  return (
    <div
      className={cn(
        baseStyles,
        variantStyles,
        stateStyles,
        'px-2 py-1 rounded-full flex items-center gap-2 cursor-pointer duration-300 w-fit',
        className
      )}
    >
      <Image
        width={imageWidth}
        height={imageHeight}
        {...props}
        className={cn('object-contain shrink-0', imageClassName)}
      />
      <Text className={cn('truncate max-w-[120px]', textClassName)}>
        {text}
      </Text>
      {variant === 'tag' && (
        <Icon
          onClick={handleCloseTag}
          className='cursor-pointer shrink-0'
          icon='material-symbols:close-rounded'
        />
      )}
    </div>
  )
}
