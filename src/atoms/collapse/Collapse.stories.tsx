import type { Meta, StoryObj } from '@storybook/react'
import Collapse from "./index"

const meta = {
    title: 'Atoms/Collapse',
    component: Collapse,
} satisfies Meta<typeof Collapse>

export default meta
type Story = StoryObj<typeof meta>


const items: App.Atoms.CollapseProps['items'] = [
    {
      key: 1,
      label: 'This is panel header 1',
      children: <p>Massa sociosqu porta aptent fames leo libero sem molestie vel etiam urna iaculis praesent ultricies congue nullam accumsan sodales</p>,
    },
    {
      key: 2,
      label: 'This is panel header 2',
      children: <p>In nec posuere sit euismod rutrum mauris proin himenaeos vivamus diam vel nulla sodales lorem imperdiet ornare scelerisque lobortis</p>,
    },
    {
      key: 3,
      label: 'This is panel header 3',
      children: <p>Parturient netus eros luctus libero quam at proin dis ex potenti in sollicitudin massa tortor et mollis nullam dignissim</p>,
    },
  ];

export const Default: Story = {
    args: {
        items: items,
        defaultActiveKey: [1],
    }
}