import { forwardRef, useCallback, useImperativeHandle, useMemo } from "react";
import { Dropdown as ADropdown, Menu } from "antd";
import type { MenuProps } from "antd";
import { cn } from "@/utils";

export const Dropdown = forwardRef<Atoms.DropdownRef, Atoms.DropDownProps>(
  (
    {
      options = [],
      onOptionSelect,
      overlayClassName,
      setVisible,
      className,
      ...props
    },
    ref
  ) => {
    useImperativeHandle(
      ref,
      () => ({
        openDropdown: () => setVisible?.(true),
        closeDropdown: () => setVisible?.(false),
      }),
      []
    );

    const handleMenuClick: MenuProps["onClick"] = useCallback(
      (e: { key: string }) => {
        if (onOptionSelect) {
          onOptionSelect(e.key);
        }
        setVisible?.(false);
      },
      [onOptionSelect]
    );

    const renderMenuItems = useMemo(
      () => (options: Atoms.Option[]) => {
        return options.map((option: Atoms.Option) => {
          if (option && option.children) {
            return (
              <Menu.SubMenu
                key={option.key}
                title={option.label}
                icon={option.icon}
              >
                {renderMenuItems(option.children)}
              </Menu.SubMenu>
            );
          }
          return (
            <Menu.Item
              danger={option.danger}
              key={option.key}
              icon={option.icon}
            >
              {option.label}
            </Menu.Item>
          );
        });
      },
      [options]
    );

    const menu = (
      <Menu onClick={handleMenuClick}>{renderMenuItems(options)}</Menu>
    );

    return (
      <ADropdown
        {...props}
        className={cn("", className)}
        overlay={menu}
        overlayClassName={overlayClassName}
      ></ADropdown>
    );
  }
);
