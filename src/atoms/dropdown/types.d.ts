declare namespace Atoms {
  type AntdDropDownProps = import("antd").DropDownProps;

  export interface DropDownProps extends AntdDropDownProps {
    options: MenuProps["items"];
    onOptionSelect?: (key: string) => void;
    setVisible?: (visible: boolean) => void;
  }
  export interface DropdownRef {
    openDropdown: () => void;
    closeDropdown: () => void;
  }

  export interface Option {
    key: string
    label: string
    icon?: React.ReactNode
    children?: Option[]
    danger?: boolean
    optionClick: () => void
  }
}
