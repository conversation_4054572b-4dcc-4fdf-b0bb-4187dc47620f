import type { Meta, StoryObj } from "@storybook/react";
import Progress from ".";

const meta = {
  title: 'Atoms/Progress',
  component: Progress
} satisfies Meta<typeof Progress>;
export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    percent: 70,
    status: 'active'
  }
}

export const Circle: Story = {
  args: {
    type: 'circle',
    percent: 70,
    status: 'exception'
  }
}

