import { Meta, StoryObj } from '@storybook/react'
import Avatar from './index'
import { Icon } from '@iconify/react'
import AvatarImage from './image.jpg'

const meta = {
  title: 'Atoms/Avatar',
  component: Avatar
} satisfies Meta<typeof Avatar>

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    size: 64,
    icon: <Icon icon="icon-park-outline:user" />
  }
}
export const Responsive: Story = {
  args: {
    size: { xs: 24, sm: 32, md: 40, lg: 64, xl: 80, xxl: 100 },
    icon: <Icon icon="icon-park-outline:user" />
  }
}
export const Image: Story = {
  args: {
    size: { xs: 24, sm: 32, md: 40, lg: 64, xl: 80, xxl: 100 },
    src: <img src={AvatarImage}/>
  }
}