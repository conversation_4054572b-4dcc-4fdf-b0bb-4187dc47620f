declare namespace Atoms {
  type AntdTabsProps = import('antd/es/tabs').TabsProps
  type TabItemType = import('rc-tabs/lib/interface').Tab
  type AntdTabPaneProps = import('antd/es/tabs/TabPane').TabPaneProps

  export type TabPaneProps = AntdTabPaneProps & {}
  export type TabItem = TabItemType & {
    count?: number | string
    linkTo?: string
  }
  export type TabsProps = Omit<AntdTabsProps, 'items'> & {
    items?: TabItem[]
  }
  export type TabsRef = {}
}