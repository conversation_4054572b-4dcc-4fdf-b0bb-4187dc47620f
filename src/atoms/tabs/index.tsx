import React, { CSSProperties } from 'react'
import { Tabs as AntdTabs, ConfigProvider, type ThemeConfig, theme } from 'antd'
import { Link } from 'react-router'

import './custom.css'

const InternalTabs = React.forwardRef<
  Atoms.TabsRef,
  Atoms.TabsProps
>((props, ref) => {
  const { token } = theme.useToken()
  const customTheme = React.useMemo<ThemeConfig>(() => ({
    components: {
      Tabs: {
        itemHoverColor: token.colorText,
        marginSM: 8,
        horizontalItemPadding: '4px 0px'
      }
    }
  }), [token])
  const { items = [], ...restProps } = props

  const countLabelCss = React.useMemo<CSSProperties>(() => ({
    backgroundColor: token.colorFillSecondary,
    fontSize: token.fontSizeSM,
    padding: '0px 4px',
    borderRadius: token.borderRadiusLG,
    minWidth: 16,
    textAlign: 'center'
  }), [token])

  const tabs = React.useMemo(() => {
    return items.map(item => {
      if (item.hasOwnProperty('count') || item.hasOwnProperty('linkTo')) {
        const { icon, count, label, linkTo, ...rest } = item

        const title = (
          <>
            {icon}
            <span>{label}</span>
            {count != undefined && count != null && <span style={countLabelCss}>{count}</span>}
          </>
        )

        return {
          ...rest,
          label: linkTo ? (
            <Link to={linkTo} className='select-none flex items-center gap-2 hover:text-[unset]'>
              {title}
            </Link>
          ) : (
            <div className='select-none flex items-center gap-2'>{title}</div>
          )
        }
      }
      return item
    })
  }, [items, countLabelCss])

  React.useImperativeHandle(ref, () => ({}), [])

  return (
    <ConfigProvider theme={customTheme}>
      <AntdTabs tabBarGutter={8} items={tabs} {...restProps} />
    </ConfigProvider>
  )
})

type Tabs = typeof InternalTabs & {
  TabPane: typeof AntdTabs.TabPane
}

const Tabs = InternalTabs as Tabs

Tabs.TabPane = AntdTabs.TabPane

Tabs.displayName = 'Tabs'

export default React.memo(Tabs)
