.ant-tabs-tab .ant-tabs-tab-btn {
  display: flex;
  align-items: center;

  border-width: 1px;
  border-style: solid;

  border-radius: 6px;
  padding: 2px 12px;
  margin-bottom: 4px;
}

.ant-tabs-tab:not(:hover):not(.ant-tabs-tab-active) .ant-tabs-tab-btn {
  border-color: transparent;
}

.ant-tabs-tab:hover:not(.ant-tabs-tab-active) .ant-tabs-tab-btn {
  border-color: var(--ant-color-border-secondary);
}

.ant-tabs-content-holder .ant-tabs-content, .ant-tabs-content-holder .ant-tabs-tabpane {
  height: 100%;
}
