import React from 'react'
import ReactQuill from 'react-quill-new'

const EditorInternal = React.forwardRef<
  Atoms.EditorRef,
  Atoms.EditorProps
>((props, ref) => {
  return (
    <ReactQuill ref={ref as React.LegacyRef<ReactQuill>} {...props} />
  )
})

const sanitizeTagContent = (value: string) => {
  return value.replace(/(<\/?[^>]+(>|$)|&nbsp;|\s)/g, "")
}

type EditorType = typeof EditorInternal & {
  sanitizeTagContent: typeof sanitizeTagContent
}

const Editor = EditorInternal as EditorType
Editor.sanitizeTagContent = sanitizeTagContent

export default Editor
