import type { Meta, StoryObj } from '@storybook/react'
import Editor from './index'

const meta = {
  title: 'Atoms/Editor',
  component: Editor
} satisfies Meta<typeof Editor>

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    theme: 'snow',
    value: 'In imperdiet molestie vitae lorem praesent leo pretium',
    placeholder: 'Enter the content ...',
  }
}

export const BubbleTheme: Story = {
  args: {
    theme: 'bubble',
    value: 'In imperdiet molestie vitae lorem praesent leo pretium',
    placeholder: 'Enter the content ...',
  }
}