import type { <PERSON>a, <PERSON>Obj } from "@storybook/react";
import Timeline from ".";
import { Icon } from "@iconify/react";

const defaultItems = [
  {
    children: "Create a services site 2015-09-01"
  },
  {
    children: "Solve initial network problems 2015-09-01"
  },
  {
    children: "Technical testing 2015-09-01"
  },
  {
    children: "Network problems being solved 2015-09-01"
  }
];

const colorsItems = [
  {
    color: "green",
    children: "Create a services site 2015-09-01"
  },
  {
    color: "green",
    children: "Create a services site 2015-09-01"
  },
  {
    color: "red",
    children: (
      <>
        <p>Solve initial network problems 1</p>
        <p>Solve initial network problems 2</p>
        <p>Solve initial network problems 3 2015-09-01</p>
      </>
    )
  },
  {
    children: (
      <>
        <p>Technical testing 1</p>
        <p>Technical testing 2</p>
        <p>Technical testing 3 2015-09-01</p>
      </>
    )
  },
  {
    color: "gray",
    children: (
      <>
        <p>Technical testing 1</p>
        <p>Technical testing 2</p>
        <p>Technical testing 3 2015-09-01</p>
      </>
    )
  },
  {
    color: "gray",
    children: (
      <>
        <p>Technical testing 1</p>
        <p>Technical testing 2</p>
        <p>Technical testing 3 2015-09-01</p>
      </>
    )
  }
];

const meta = {
  title: "Atoms/Timeline",
  component: Timeline,
  parameters: {
    layout: "centered"
  },
  argTypes: {
    mode: {
      options: ["left", "alternate", "right"],
      control: "radio"
    },
    reverse: {
      control: "boolean"
    }
  },
  args: {}
} satisfies Meta<typeof Timeline>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    items: defaultItems,
    reverse: false
  }
};

export const LastNodeAndReversing: Story = {
  args: {
    items: defaultItems,
    pending: "Recording...",
    reverse: true
  }
};

export const Colors: Story = {
  args: {
    items: colorsItems,
    reverse: false
  }
};

const alternateItems = [
  {
    children: "Create a services site 2015-09-01"
  },
  {
    children: "Solve initial network problems 2015-09-01",
    color: "green"
  },
  {
    dot: <Icon icon="ant-design:clock-circle-outlined" />,
    children: `Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.`
  },
  {
    color: "red",
    children: "Network problems being solved 2015-09-01"
  },
  {
    children: "Create a services site 2015-09-01"
  },
  {
    dot: <Icon icon="ant-design:clock-circle-outlined" />,
    children: "Technical testing 2015-09-01"
  }
];

export const Alternate: Story = {
  args: {
    mode: "alternate",
    items: alternateItems,
    reverse: false
  }
};
