import React from 'react'
import type { Meta, StoryObj } from '@storybook/react'
import Tag from "./index"
import { Icon } from '@iconify/react'
import Avatar from '../avatar'
import AvatarImage from './avatar.png'

const meta = {
  title: 'Atoms/Tag',
  component: Tag,
} satisfies Meta<typeof Tag>

export default meta
type Story = StoryObj<typeof meta>

const DefaultComponent = React.memo(() => {
  return (
    <div>
      <Tag icon={<Avatar size={12} src={<img src={AvatarImage}/>} />}>Mansa Group</Tag>
    </div>
  )
})
const OutlineComponent = React.memo(() => {
  return (
    <div>
      <Tag icon={<Avatar size={12} src={<img src={AvatarImage}/>} />} type='outline'>Mansa Group</Tag>
    </div>
  )
})
const CloseComponent = React.memo(() => {
  return (
    <div>
      <Tag icon={<Avatar size={12} src={<img src={AvatarImage}/>} />} type='outline' closable={true}>Mansa Group</Tag>
    </div>
  )
})
const BlurComponent = React.memo(() => {
  return (
    <div>
      <Tag icon={<Icon icon="icon-park-outline:user"/>} type='blur'>Mansa Group</Tag>
    </div>
  )
})
const TabComponent = React.memo(() => {
  return (
    <div>
      <Tag icon={<Icon icon="icon-park-outline:user"/>} type='tab'>Mansa Group</Tag>
    </div>
  )
})
const TabActiveComponent = React.memo(() => {
  return (
    <div>
      <Tag icon={<Icon icon="icon-park-outline:user"/>} type='tab-active'>Mansa Group</Tag>
    </div>
  )
})

export const Default: Story = {
  args: {
    className: ''
  },
  render: () => <DefaultComponent/>
}

export const Outline: Story = {
  args: {
    className: ''
  },
  render: () => <OutlineComponent/>
}

export const Close: Story = {
  args: {
    className: ''
  },
  render: () => <CloseComponent/>
}

export const Blur: Story = {
  args: {
    className: ''
  },
  render: () => <BlurComponent/>
}

export const Tab: Story = {
  args: {
    className: ''
  },
  render: () => <TabComponent/>
}

export const TabActive: Story = {
  args: {
    className: ''
  },
  render: () => <TabActiveComponent/>
}
