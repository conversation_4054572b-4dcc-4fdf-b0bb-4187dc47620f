.tag-normal {
  @apply inline-flex items-center gap-2 cursor-pointer;
}

.tag-normal:hover {
  @apply ease-in-out bg-light-darker;
}

.tag-default {
  @apply tag-normal border-0 bg-[transparent] rounded-xl;
}

.tag-outline {
  @apply tag-normal border rounded-xl;
}

.tag-outline-close {
  @apply bg-success rounded-xl;
}

.tag-blur {
  @apply tag-default text-gray-light;
}

.tag-tab {
  @apply tag-default text-default-dark;
}

.tag-tab-active {
  @apply tag-normal ease-in-out mb-[0.5em] border border-secondary px-3 py-1 relative bg-[transparent] after:absolute after:bottom-[-0.5em] after:left-0 after:content-[''] after:w-full after:h-[1px] after:bg-gray-light;
}

.tag-tab-active:hover {
  @apply bg-light-darker;
}