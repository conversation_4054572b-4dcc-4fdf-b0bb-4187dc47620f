import { Tag as AntTag } from 'antd'
import './style.css'

import { cn } from '@/utils';

const Tag = (props: Atoms.TagProps) => {
    const { extra, type, children, className, ...restProps } = props

    let customClass: string = type ? `tag-${type}` : 'tag-default'

    return (<AntTag bordered {...restProps} className={cn(customClass, 'border', className)}>
        {children ?? ''}
        {extra ?? ''}
    </AntTag>)
}

export default Tag
