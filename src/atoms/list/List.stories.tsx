import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import List from ".";

const data = [
  "Racing car sprays burning fuel into crowd.",
  "Japanese princess to wed commoner.",
  "Australian walks 100km after outback crash.",
  "Man charged over missing wedding girl.",
  "Los Angeles battles huge wildfires."
];

const meta = {
  title: "Atoms/List",
  component: List
} satisfies Meta<typeof List>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    dataSource: data,
    renderItem: (item) => <List.Item>{item as React.ReactNode}</List.Item>
  }
};
