declare namespace Atoms {
  type AntdSelectProps = import('antd').SelectProps
  export type SelectProps<T, D> = AntdSelectProps<T, D> & {}
  export type SelectRef = {}
}

declare namespace App.Atoms.Select {
  export type Props<TValue, TOption> = import('antd').SelectProps<TValue, TOption>
  export type Refs = import('antd').RefSelectProps
  export type OptionItem = NonNullable<Props['options']>[0] & {
    icon?: React.ReactNode
    extra?: React.ReactNode | ((option: OptionItem) => React.ReactNode)
    items?: OptionItem[]
    isLast?: boolean
  }

  export type SearchProps = Omit<import('antd').SelectProps, 'dropdownRender'> & {
    dropdownRender?(menu: React.ReactNode, header: React.ReactNode, extra?: React.ReactNode): React.ReactNode
    dropdownHeaderRender?(inputSearch: React.ReactNode): React.ReactNode
    dropdownExtraRender?(): React.ReactNode
    inputSearchProps?: import('antd').InputProps
    options: OptionItem[]
  }
  export type SearchRef = Refs
}
