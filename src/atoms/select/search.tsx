import React, { CSSProperties } from 'react'
import { Select, ConfigProvider, theme } from 'antd'
import type { SelectProps, InputProps, InputRef, ThemeConfig } from 'antd'
import { isFunction } from 'lodash'
import { Icon } from '@iconify/react'
import { Input, Divider, Button } from '@/atoms'
import './custom.css'

const selectedIcon = (<Icon icon='lets-icons:check-round-fill' className='text-lg'/>)
const defaultNotFoundContent = (<span className='text-center'>No item found</span>)
const getNodeData = (element: HTMLElement, key: string): string | undefined => {
  if (!element) return undefined

  if (element.dataset && element.dataset[key]) {
    return element.dataset[key]
  }

  if (!element.parentElement) return undefined

  return getNodeData(element.parentElement, key)
}

const Search = React.forwardRef<
  App.Atoms.Select.SearchRef,
  App.Atoms.Select.SearchProps
>((props, ref) => {
  const {
    children,
    showSearch, // disabled showSearch prop. Alway show input search in dropdown popup
    inputSearchProps,
    options,
    mode,
    onSelect,
    dropdownRender,
    dropdownHeaderRender,
    dropdownExtraRender,
    ...restProps
  } = props

  const [keyword, setKeyword] = React.useState<string | undefined>()
  const inputRef = React.useRef<InputRef>(null)
  const { token } = theme.useToken()
  const [selectedOpton, setSelectedOption] =
    React.useState<App.Atoms.Select.OptionItem>({})
  const [currentOptions, setCurrentOptions] = React.useState(options)
  const [currentOptionsPath, setCurrentOptionsPath] = React.useState<number[]>(
    []
  )

  const customTheme = React.useMemo<ThemeConfig>(() => ({
      components: {
        Select: {
          optionSelectedBg: 'transparent',
          activeOutlineColor: 'transparent',
          hoverBorderColor: token.colorBorderSecondary,
          activeBorderColor: token.colorBorderSecondary,
          colorIcon: token.colorIcon,
          optionPadding: 0,
          optionSelectedFontWeight: 400
        }
      }
    }),
    [token]
  )

  React.useEffect(() => {
    setCurrentOptions(options)
  }, [options])

  const hasIcon = React.useMemo(() => {
    return currentOptions.some(option => option.hasOwnProperty('icon'))
  }, [currentOptions])

  const hasExtra = React.useMemo(() => {
    if (mode === 'multiple') return false // disabled on multiple mode
    return currentOptions.some(option => option.hasOwnProperty('extra') || option.items?.length)
  }, [currentOptions, mode])

  const onDropdownOpenChange = React.useCallback<NonNullable<SelectProps['onDropdownVisibleChange']>>((open) => {
    if (open) {
      setTimeout(() => inputRef.current?.focus(), 100)
    }
  }, [])

  const onKeywordChange = React.useCallback<NonNullable<InputProps['onChange']>>((e) => {
    setKeyword(e.target.value?.trim())
  }, [])

  const onPrevClick = React.useCallback(() => {
    setCurrentOptionsPath((prevPath) => {
      const newPath = prevPath.slice(0, -1)
      let newOptions = options
      let prevOption = null

      // Navigate to the parent level
      for (let i = 0; i < newPath.length; i++) {
        const index = newPath[i]
        prevOption = newOptions[index]
        newOptions = newOptions[index].items || []
      }

      // Set the parent item as selected option
      setSelectedOption(prevOption || {})
      setCurrentOptions(newOptions)
      return newPath
    })
  }, [options, currentOptions, currentOptionsPath])

  const dropdownRenderHandle = React.useCallback<
    NonNullable<App.Atoms.Select.Props['dropdownRender']>
  >((menu) => {
      const inputSearch = (
        <Input
          variant='borderless'
          placeholder='Search...'
          {...(inputSearchProps || {})}
          ref={inputRef}
          onChange={onKeywordChange}
        />
      )
      let header: React.ReactNode = (
        <>
          {inputSearch}
          <Divider />
        </>
      )
      let extra: React.ReactNode = undefined

      if (isFunction(dropdownHeaderRender)) {
        header = dropdownHeaderRender(inputSearch)
      }

      if (isFunction(dropdownExtraRender)) {
        extra = dropdownExtraRender()
      }

      if (isFunction(dropdownRender)) {
        dropdownRender(menu, inputSearch, extra)
      }

      let prev
      if (currentOptionsPath.length) {
        let option: App.Atoms.Select.OptionItem = {}
        let currentOption = options
        for (const [_, index] of currentOptionsPath.entries()) {
          option = currentOption[index] as App.Atoms.Select.OptionItem
          if (index < currentOptionsPath.length) {
            currentOption = option?.items || []
          }
        }
        const style: CSSProperties = { backgroundColor: token.colorBgLayout }
        prev = (
          <>
            <div className='flex items-center gap-1 text-small'>
              <Button
                style={style}
                onClick={onPrevClick}
                className='!p-0'
                size='small'
                type={'text'}
                shape='circle'
              >
                <Icon icon='icon-park-outline:left' />
              </Button>
              {selectedOpton?.label}
            </div>
            <Divider />
          </>
        )
      }

      return (
        <div>
          {prev}
          {header}
          {menu}
          {extra}
        </div>
      )
  }, [
      inputSearchProps,
      selectedOpton,
      currentOptionsPath,
      options,
      token,
      dropdownRender,
      dropdownHeaderRender,
      dropdownExtraRender,
      onKeywordChange,
      onPrevClick
    ]
  )

  const resetDropdownState = React.useCallback(() => {
    setTimeout(() => {
      setCurrentOptionsPath([])
      setCurrentOptions(options)
    }, 200)
  }, [options])

  const onOptionItemClick = React.useCallback<
    React.MouseEventHandler<HTMLDivElement>
  >(
    (e) => {
      const value = getNodeData(e.target as HTMLElement, 'value')
      if (!value) return

      const optionIndex = currentOptions.findIndex(
        (item) => item.value === value
      )
      const selectedOption = currentOptions[
        optionIndex
      ] as App.Atoms.Select.OptionItem

      setSelectedOption(selectedOption)

      // Handle nested navigation
      if (selectedOption?.items?.length) {
        e.preventDefault()
        e.stopPropagation()
        setCurrentOptionsPath((old) => [...old, optionIndex])
        setCurrentOptions(selectedOption.items)
        return
      }

      // Only reset and add column when selecting a leaf node (no items) and we're in a nested path
      if (!selectedOption?.items?.length) {
        resetDropdownState()
        onSelect?.(value, selectedOption)
      }
    },
    [
      currentOptions,
      resetDropdownState,
      onSelect
    ]
  )

  const customOptionRender = React.useCallback<
    NonNullable<SelectProps['optionRender']>
  >(
    (option) => {
      const oriOption = option.data as App.Atoms.Select.OptionItem

      // NOTE: TailwindCSS has no effect
      // let gridTemplate = [
      //   hasIcon && `16px`,
      //   `minmax(50px,1fr)`,
      //   hasExtra && `24px`
      // ].filter(Boolean).join('_')
      // let className = `w-full text-ellipsis overflow-hidden grid grid-cols-[${gridTemplate}] gap-1 items-center`

      let gridTemplate = `grid-cols-[minmax(50px,1fr)]`
      if (hasIcon && hasExtra) {
        gridTemplate = `grid-cols-[16px_minmax(50px,1fr)_auto]`
      } else if (hasIcon && !hasExtra) {
        gridTemplate = `grid-cols-[16px_minmax(50px,1fr)]`
      } else if (!hasIcon && hasExtra) {
        gridTemplate = `grid-cols-[minmax(50px,1fr)_auto]`
      }

      let extra: React.ReactNode = undefined
      if (oriOption.extra) {
        if (oriOption.items?.length) {
          const style: CSSProperties = { color: token.colorTextTertiary }
          extra = (
            <div className='flex items-center -mr-1' style={style}>
              {oriOption.items.length}
              <Icon icon='icon-park-outline:right' />
            </div>
          )
        } else {
          extra = isFunction(oriOption.extra)
            ? oriOption.extra(oriOption)
            : oriOption.extra
        }
      }

      return (
        <div>
          {oriOption.isLast && <Divider />}
          <div>
            <div
              key={option?.key}
              data-value={option?.value}
              className={`w-full grid ${gridTemplate} gap-1 items-center px-[8px] py-[5px]`}
              onClick={onOptionItemClick}
            >
              {oriOption.icon}
              <div className='mr-2 w-full text-ellipsis overflow-hidden'>
                {option?.label}
              </div>
              {!!extra && hasExtra && extra}
            </div>
          </div>
        </div>
      )
    },
    [hasIcon, hasExtra, token, onOptionItemClick]
  )

  React.useImperativeHandle(ref, () => ({}), [])

  return (
    <ConfigProvider theme={customTheme}>
      <Select
        showSearch={false}
        allowClear
        mode={mode}
        options={currentOptions}
        searchValue={keyword}
        optionFilterProp='label'
        popupClassName='shadow-default'
        notFoundContent={defaultNotFoundContent}
        suffixIcon={false}
        defaultActiveFirstOption={false}
        menuItemSelectedIcon={hasExtra ? undefined : selectedIcon}
        dropdownRender={dropdownRenderHandle}
        onDropdownVisibleChange={onDropdownOpenChange}
        optionRender={customOptionRender}
        {...restProps}
      />
    </ConfigProvider>
  )
})

Search.displayName = 'Select.Search'

export default React.memo(Search)
