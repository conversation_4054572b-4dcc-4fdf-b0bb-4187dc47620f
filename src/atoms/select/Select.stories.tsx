import type { <PERSON>a, StoryObj } from "@storybook/react";
import Select from ".";

const meta = {
  title: "Atoms/Select",
  component: Select
} satisfies Meta<typeof Select>;
export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    defaultValue: "jack",
    options: [
      { value: "jack", label: "<PERSON>" },
      { value: "lucy", label: "<PERSON>" },
      { value: "Yi<PERSON><PERSON>", label: "yiminghe" },
      { value: "disabled", label: "Disabled", disabled: true }
    ]
  }
};
