declare namespace Atoms {
    type AntCheckboxProps = import('antd').CheckboxProps
    type AntCheckboxGroupProps = import('antd/es/checkbox').CheckboxGroupProps

    export interface CheckboxGroupProps extends AntCheckboxGroupProps {}

    export interface CheckboxProps extends AntCheckboxProps {}
}
declare namespace App.Atoms {
    type AntCheckboxProps = import('antd').CheckboxProps
    type AntCheckboxGroupProps = import('antd/es/checkbox').CheckboxGroupProps

    export interface CheckboxGroupProps extends AntCheckboxGroupProps {}

    export interface CheckboxProps extends AntCheckboxProps {}
}