import React from 'react'
import type { Meta, StoryObj } from '@storybook/react'
import Checkbox from "."

const meta = {
  title: 'Atoms/Checkbox',
  component: Checkbox,
} satisfies Meta<typeof Checkbox>

export default meta
type Story = StoryObj<typeof meta>

const Example = React.memo(() => {
  return (<Checkbox>Checkbox</Checkbox>)
})

export const Default: Story = {
  args: {
  },
  render: () => <Example />
}