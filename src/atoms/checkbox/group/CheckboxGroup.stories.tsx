import React from 'react'
import type { Meta, StoryObj } from '@storybook/react'
import Checkbox from ".."
import type { GetProp } from 'antd'

const { Group } = Checkbox

const meta = {
    title: 'Atoms/Checkbox:Group',
    component: Group,
} satisfies Meta<typeof Group>

export default meta
type Story = StoryObj<typeof meta>

const plainOptions = ['Apple', 'Pear', 'Orange'];
const onChange: GetProp<typeof Checkbox.Group, 'onChange'> = (checkedValues) => {
  console.log(checkedValues);
}

const Example = React.memo(() => {
  return (<Checkbox.Group options={plainOptions} defaultValue={['Apple']} onChange={onChange} />
  )
})

export const Default: Story = {
    args: {
    },
    render: () => <Example />
}