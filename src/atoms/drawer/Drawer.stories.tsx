import React from 'react'
import type { Meta, StoryObj } from '@storybook/react'
import Drawer from "."
import Button from '../button'

const meta = {
    title: 'Atoms/Drawer',
    component: Drawer,
} satisfies Meta<typeof Drawer>

export default meta
type Story = StoryObj<typeof meta>


const Example = () => {
  const [open, setOpen] = React.useState(false);
  const showDrawer = () => {
    setOpen(true);
  }
  
  const onClose = () => {
    setOpen(false);
  }
  
  return <div>
    <Button type="primary" onClick={showDrawer}>
        Open
      </Button>
      <Drawer title="Basic Drawer" onClose={onClose} open={open}>
        <p>Some contents...</p>
        <p>Some contents...</p>
        <p>Some contents...</p>
      </Drawer>
  </div>
}
export const Default: Story = {
    args: {},
    render: () => <Example/>
}